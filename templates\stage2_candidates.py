"""
Stage 2 candidate management with guided placement.

Provides semi-automatic promotion from stage1 with intelligent suggestions.
"""

import os
import glob
import shutil
import re
from typing import Dict, List, Any, Optional
from .catalog import LIFECYCLE_STAGES, TemplateConfigMD, generate_catalog


class CandidateManager:
    """Semi-automatic management for validated templates."""
    
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.stage1_dir = os.path.join(self.script_dir, "md", "stage1")
        self.stage2_dir = os.path.join(self.script_dir, "md", "stage2")
    
    def promote_from_stage1(self, stage1_id: str, auto_accept: bool = False) -> str:
        """Promote stage1 template to stage2 with guided placement."""
        
        # Validate stage1 template exists
        stage1_file = self._find_template_file(stage1_id)
        if not stage1_file:
            raise FileNotFoundError(f"Stage1 template {stage1_id} not found")
        
        # Analyze template for placement suggestions
        analysis = self._analyze_template(stage1_id, stage1_file)
        suggestions = self._generate_placement_suggestions(analysis)
        
        if auto_accept and len(suggestions) == 1:
            chosen_id = suggestions[0]["id"]
            print(f"Auto-accepting suggestion: {chosen_id}")
        else:
            chosen_id = self._interactive_placement_selection(suggestions, stage1_id)
        
        # Execute promotion
        new_id = self._execute_promotion(stage1_id, stage1_file, chosen_id)
        return new_id
    
    def _find_template_file(self, template_id: str) -> Optional[str]:
        """Find template file by ID in stage1 directory."""
        pattern = os.path.join(self.stage1_dir, f"{template_id}-*.md")
        matches = glob.glob(pattern)
        return matches[0] if matches else None
    
    def _analyze_template(self, template_id: str, filepath: str) -> Dict[str, Any]:
        """Analyze template for placement suggestions."""
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract template components
        config = TemplateConfigMD()
        stage = config.get_template_stage(template_id)
        category = config.get_template_category(template_id)
        
        # Basic content analysis
        title_match = re.search(r'\[(.*?)\]', content)
        title = title_match.group(1) if title_match else "Unknown"
        
        return {
            "template_id": template_id,
            "filepath": filepath,
            "content": content,
            "title": title,
            "stage": stage,
            "category": category,
            "content_length": len(content),
            "has_sequence": '-' in template_id and len(template_id.split('-')) >= 2
        }
    
    def _generate_placement_suggestions(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate intelligent placement suggestions."""
        suggestions = []
        
        # Get next available ID in stage2
        next_id = self._get_next_available_stage2_id()
        suggestions.append({
            "id": next_id,
            "reason": "Next available in stage2",
            "confidence": 0.8
        })
        
        # Category-based suggestion (if we implement category ranges in stage2)
        category = analysis["category"]
        if category != "unknown":
            category_id = self._suggest_category_based_id(category)
            if category_id and category_id != next_id:
                suggestions.append({
                    "id": category_id,
                    "reason": f"Category-based placement for {category}",
                    "confidence": 0.7
                })
        
        # Sequence-based suggestion
        if analysis["has_sequence"]:
            sequence_id = self._suggest_sequence_based_id(analysis)
            if sequence_id and sequence_id not in [s["id"] for s in suggestions]:
                suggestions.append({
                    "id": sequence_id,
                    "reason": "Sequence-based placement",
                    "confidence": 0.9
                })
        
        return sorted(suggestions, key=lambda x: x["confidence"], reverse=True)
    
    def _get_next_available_stage2_id(self) -> int:
        """Find next available ID in stage2 range."""
        stage2_config = LIFECYCLE_STAGES["stage2"]
        existing_ids = self._get_existing_ids_in_range(
            stage2_config.range_start, 
            stage2_config.range_end
        )
        
        for candidate_id in range(stage2_config.range_start, stage2_config.range_end + 1):
            if candidate_id not in existing_ids:
                return candidate_id
        
        raise ValueError("No available IDs in stage2 range")
    
    def _suggest_category_based_id(self, category: str) -> Optional[int]:
        """Suggest ID based on category grouping in stage2."""
        # For now, just return next available
        # Could implement category sub-ranges in stage2 if desired
        return self._get_next_available_stage2_id()
    
    def _suggest_sequence_based_id(self, analysis: Dict[str, Any]) -> Optional[int]:
        """Suggest ID based on sequence relationships."""
        template_id = analysis["template_id"]
        
        # If this is part of a sequence, suggest keeping sequence together
        if '-' in template_id:
            base_id = template_id.split('-')[0]
            # Look for other templates in this sequence that might be in stage2
            pattern = os.path.join(self.full_templates_dir, f"2*-*{base_id}*.md")
            existing_sequence_files = glob.glob(pattern)
            
            if existing_sequence_files:
                # Extract ID from first match and suggest nearby
                first_file = os.path.basename(existing_sequence_files[0])
                try:
                    existing_id = int(first_file.split('-')[0])
                    return existing_id  # Could be same sequence
                except (ValueError, IndexError):
                    pass
        
        return None
    
    def _get_existing_ids_in_range(self, start: int, end: int) -> set:
        """Get all existing template IDs in specified range."""
        existing_ids = set()
        
        pattern = os.path.join(self.full_templates_dir, "*.md")
        for filepath in glob.glob(pattern):
            filename = os.path.basename(filepath)
            try:
                numeric_id = int(filename.split('-')[0])
                if start <= numeric_id <= end:
                    existing_ids.add(numeric_id)
            except (ValueError, IndexError):
                continue
        
        return existing_ids
    
    def _interactive_placement_selection(self, suggestions: List[Dict[str, Any]], template_id: str) -> int:
        """Interactive selection of placement option."""
        print(f"\nPromoting template {template_id} to Stage 2")
        print("Placement suggestions:")
        
        for i, suggestion in enumerate(suggestions):
            print(f"  {i+1}. ID: {suggestion['id']} - {suggestion['reason']} (confidence: {suggestion['confidence']:.1%})")
        
        while True:
            try:
                choice = input(f"\nChoose option (1-{len(suggestions)}) or enter custom ID (2000-2999): ")
                
                # Check if it's a custom ID
                if choice.isdigit() and 2000 <= int(choice) <= 2999:
                    custom_id = int(choice)
                    if custom_id not in self._get_existing_ids_in_range(2000, 2999):
                        return custom_id
                    else:
                        print(f"ID {custom_id} already exists. Choose another.")
                        continue
                
                # Check if it's a suggestion choice
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(suggestions):
                    return suggestions[choice_idx]["id"]
                else:
                    print(f"Invalid choice. Enter 1-{len(suggestions)} or custom ID.")
                    
            except ValueError:
                print("Please enter a valid number.")
    
    def _execute_promotion(self, old_id: str, old_filepath: str, new_id: int) -> str:
        """Execute promotion by moving file from stage1 to stage2 with new ID."""

        # Ensure stage2 directory exists
        os.makedirs(self.stage2_dir, exist_ok=True)

        # Read original content
        with open(old_filepath, 'r', encoding='utf-8') as f:
            content = f.read()

        # Generate new filename
        old_filename = os.path.basename(old_filepath)
        filename_parts = old_filename.split('-', 1)
        if len(filename_parts) > 1:
            new_filename = f"{new_id}-{filename_parts[1]}"
        else:
            new_filename = f"{new_id}-promoted.md"

        # Write to stage2 directory
        new_filepath = os.path.join(self.stage2_dir, new_filename)
        with open(new_filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        # Handle test prompt file if it exists
        testprompt_old = old_filepath.replace('.md', '.testprompt')
        if os.path.exists(testprompt_old):
            testprompt_new = new_filepath.replace('.md', '.testprompt')
            shutil.copy2(testprompt_old, testprompt_new)
            os.remove(testprompt_old)

        # Remove original from stage1
        os.remove(old_filepath)

        print(f"Promoted: stage1/{old_filename} -> stage2/{new_filename}")
        return str(new_id)


def promote_to_stage2(template_id: str, auto_accept: bool = False) -> str:
    """Simplified promotion interface."""
    manager = CandidateManager()
    return manager.promote_from_stage1(template_id, auto_accept)

def suggest_placement(template_id: str) -> List[Dict[str, Any]]:
    """Get placement suggestions without executing promotion."""
    manager = CandidateManager()
    
    # Find and analyze template
    template_file = manager._find_template_file(template_id)
    if not template_file:
        raise FileNotFoundError(f"Template {template_id} not found")
    
    analysis = manager._analyze_template(template_id, template_file)
    return manager._generate_placement_suggestions(analysis)

def list_stage1_templates() -> List[str]:
    """List all stage1 templates available for promotion."""
    manager = CandidateManager()
    stage1_templates = []
    
    pattern = os.path.join(manager.full_templates_dir, "1*.md")
    for filepath in glob.glob(pattern):
        filename = os.path.basename(filepath)
        try:
            template_id = filename.split('-')[0]
            if 1000 <= int(template_id) <= 1999:
                stage1_templates.append(template_id)
        except (ValueError, IndexError):
            continue
    
    return sorted(stage1_templates)

def list_stage2_templates() -> List[str]:
    """List all stage2 templates."""
    manager = CandidateManager()
    stage2_templates = []
    
    pattern = os.path.join(manager.full_templates_dir, "2*.md")
    for filepath in glob.glob(pattern):
        filename = os.path.basename(filepath)
        try:
            template_id = filename.split('-')[0]
            if 2000 <= int(template_id) <= 2999:
                stage2_templates.append(template_id)
        except (ValueError, IndexError):
            continue
    
    return sorted(stage2_templates)
