"""
Semantic Template Catalog System

This module provides semantic organization of templates while maintaining
backward compatibility with existing numeric IDs for log file connections.
"""

import os
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class TemplateCategory:
    """Defines a semantic category of templates."""
    name: str
    description: str
    direction: str  # "expand" or "compress"
    id_ranges: List[tuple]  # [(start, end), ...]
    aliases: List[str]


# Semantic category definitions
TEMPLATE_CATEGORIES = {
    "amplifiers": TemplateCategory(
        name="amplifiers",
        description="Templates that intensify, magnify, or enhance inherent qualities",
        direction="expand",
        id_ranges=[(9000, 9000), (9005, 9005)],  # 9000-series (intensity), 9005-series (flow)
        aliases=["amplify", "intensify", "magnify", "enhance", "boost"]
    ),
    
    "builders": TemplateCategory(
        name="builders",
        description="Templates that construct, create, or generate new content",
        direction="expand", 
        id_ranges=[(2000, 2999), (8000, 8999)],  # Reserved ranges for builders
        aliases=["build", "create", "generate", "construct", "compose"]
    ),
    
    "characters": TemplateCategory(
        name="characters",
        description="Templates that define roles, personas, or character traits",
        direction="expand",
        id_ranges=[(3000, 3999)],  # Reserved range for characters
        aliases=["character", "persona", "role", "identity", "voice"]
    ),
    
    "clarifiers": TemplateCategory(
        name="clarifiers", 
        description="Templates that enhance clarity, transparency, or understanding",
        direction="expand",
        id_ranges=[(9001, 9001)],  # 9001-series (clarity vectors)
        aliases=["clarify", "purify", "illuminate", "explain", "simplify"]
    ),
    
    "formatters": TemplateCategory(
        name="formatters",
        description="Templates that structure, organize, or format content",
        direction="compress",
        id_ranges=[(4000, 4999)],  # Reserved range for formatters
        aliases=["format", "structure", "organize", "layout", "arrange"]
    ),
    
    "generators": TemplateCategory(
        name="generators",
        description="Templates that produce new content from existing input",
        direction="expand",
        id_ranges=[(5000, 5999)],  # 5000-series (testing/generation)
        aliases=["generate", "produce", "create", "synthesize", "derive"]
    ),
    
    "identifiers": TemplateCategory(
        name="identifiers",
        description="Templates that classify, extract, or identify elements",
        direction="compress", 
        id_ranges=[(1000, 1999)],  # 1000-series (extractors, classifiers)
        aliases=["identify", "extract", "classify", "detect", "recognize"]
    ),
    
    "optimizers": TemplateCategory(
        name="optimizers",
        description="Templates that improve, refine, or optimize content",
        direction="compress",
        id_ranges=[(6000, 6999)],  # Reserved range for optimizers
        aliases=["optimize", "improve", "refine", "enhance", "perfect"]
    ),
    
    "reducers": TemplateCategory(
        name="reducers",
        description="Templates that compress, distill, or reduce content",
        direction="compress",
        id_ranges=[(9002, 9004)],  # 9002-9004 series (structural, transformation, meta)
        aliases=["reduce", "compress", "distill", "condense", "minimize"]
    ),
    
    "transformers": TemplateCategory(
        name="transformers",
        description="Templates that fundamentally transform or convert content",
        direction="expand",
        id_ranges=[(9003, 9003), (9006, 9006)],  # 9003-series (transformation), 9006-series (quantum)
        aliases=["transform", "convert", "translate", "morph", "transmute"]
    ),
    
    "translators": TemplateCategory(
        name="translators", 
        description="Templates that translate between formats, languages, or representations",
        direction="expand",
        id_ranges=[(7000, 7999)],  # Reserved range for translators
        aliases=["translate", "convert", "adapt", "bridge", "transpose"]
    )
}


class SemanticCatalog:
    """Enhanced catalog with semantic organization and backward compatibility."""
    
    def __init__(self, base_catalog: Dict[str, Any]):
        """Initialize with existing catalog data."""
        self.base_catalog = base_catalog
        self.semantic_mapping = self._build_semantic_mapping()
    
    def _build_semantic_mapping(self) -> Dict[str, Dict[str, Any]]:
        """Build semantic mapping from existing templates."""
        mapping = {}
        
        # Initialize categories
        for category_name in TEMPLATE_CATEGORIES:
            mapping[category_name] = {
                "templates": {},
                "sequences": {},
                "metadata": TEMPLATE_CATEGORIES[category_name]
            }
        
        # Map existing templates to categories
        templates = self.base_catalog.get("templates", {})
        sequences = self.base_catalog.get("sequences", {})
        
        for template_id, template_data in templates.items():
            category = self._categorize_template(template_id)
            if category:
                mapping[category]["templates"][template_id] = template_data
        
        for sequence_id, sequence_data in sequences.items():
            category = self._categorize_sequence(sequence_id)
            if category:
                mapping[category]["sequences"][sequence_id] = sequence_data
        
        return mapping
    
    def _categorize_template(self, template_id: str) -> Optional[str]:
        """Categorize a template based on its ID."""
        # Extract numeric ID from template_id (e.g., "1031-a-form_classifier" -> 1031)
        try:
            numeric_id = int(template_id.split('-')[0])
        except (ValueError, IndexError):
            return None
        
        # Find matching category
        for category_name, category in TEMPLATE_CATEGORIES.items():
            for start, end in category.id_ranges:
                if start <= numeric_id <= end:
                    return category_name
        
        return None
    
    def _categorize_sequence(self, sequence_id: str) -> Optional[str]:
        """Categorize a sequence based on its ID."""
        try:
            numeric_id = int(sequence_id)
        except ValueError:
            return None
        
        # Find matching category
        for category_name, category in TEMPLATE_CATEGORIES.items():
            for start, end in category.id_ranges:
                if start <= numeric_id <= end:
                    return category_name
        
        return None
    
    def get_category_templates(self, category: str) -> Dict[str, Any]:
        """Get all templates in a category."""
        return self.semantic_mapping.get(category, {}).get("templates", {})
    
    def get_category_sequences(self, category: str) -> Dict[str, Any]:
        """Get all sequences in a category."""
        return self.semantic_mapping.get(category, {}).get("sequences", {})
    
    def find_by_alias(self, alias: str) -> List[str]:
        """Find categories that match an alias."""
        matching_categories = []
        
        for category_name, category in TEMPLATE_CATEGORIES.items():
            if alias.lower() in [a.lower() for a in category.aliases]:
                matching_categories.append(category_name)
        
        return matching_categories
    
    def get_expand_categories(self) -> List[str]:
        """Get all categories that expand content."""
        return [name for name, cat in TEMPLATE_CATEGORIES.items() if cat.direction == "expand"]
    
    def get_compress_categories(self) -> List[str]:
        """Get all categories that compress content."""
        return [name for name, cat in TEMPLATE_CATEGORIES.items() if cat.direction == "compress"]
    
    def suggest_next_id(self, category: str) -> Optional[int]:
        """Suggest next available ID in a category range."""
        if category not in TEMPLATE_CATEGORIES:
            return None
        
        category_info = TEMPLATE_CATEGORIES[category]
        existing_ids = set()
        
        # Collect existing IDs in this category
        for template_id in self.get_category_templates(category):
            try:
                numeric_id = int(template_id.split('-')[0])
                existing_ids.add(numeric_id)
            except (ValueError, IndexError):
                continue
        
        # Find next available ID in any range
        for start, end in category_info.id_ranges:
            for candidate_id in range(start, end + 1):
                if candidate_id not in existing_ids:
                    return candidate_id
        
        return None
    
    def export_semantic_catalog(self) -> Dict[str, Any]:
        """Export enhanced catalog with semantic organization."""
        return {
            "catalog_meta": {
                **self.base_catalog.get("catalog_meta", {}),
                "semantic_version": "1.0",
                "categories": list(TEMPLATE_CATEGORIES.keys())
            },
            "templates": self.base_catalog.get("templates", {}),
            "sequences": self.base_catalog.get("sequences", {}),
            "semantic_mapping": self.semantic_mapping,
            "categories": {name: {
                "description": cat.description,
                "direction": cat.direction,
                "aliases": cat.aliases,
                "id_ranges": cat.id_ranges
            } for name, cat in TEMPLATE_CATEGORIES.items()}
        }


def enhance_existing_catalog(catalog_path: str) -> SemanticCatalog:
    """Enhance existing catalog with semantic organization."""
    with open(catalog_path, 'r', encoding='utf-8') as f:
        base_catalog = json.load(f)
    
    return SemanticCatalog(base_catalog)


def save_semantic_catalog(semantic_catalog: SemanticCatalog, output_path: str):
    """Save enhanced catalog with semantic mapping."""
    enhanced_data = semantic_catalog.export_semantic_catalog()
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(enhanced_data, f, indent=2, ensure_ascii=False)


if __name__ == "__main__":
    # Example usage
    catalog_path = "lvl1.md.templates.json"
    if os.path.exists(catalog_path):
        semantic_catalog = enhance_existing_catalog(catalog_path)
        save_semantic_catalog(semantic_catalog, "lvl1.md.semantic.json")
        
        print("Semantic catalog created!")
        print("\nCategories:")
        for category in TEMPLATE_CATEGORIES:
            templates = semantic_catalog.get_category_templates(category)
            sequences = semantic_catalog.get_category_sequences(category)
            print(f"  {category}: {len(templates)} templates, {len(sequences)} sequences")
    else:
        print(f"Base catalog not found: {catalog_path}")
