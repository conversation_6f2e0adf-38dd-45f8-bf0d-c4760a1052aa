# System Architecture: How Everything Ties Together

## Overview

The AI Systems framework operates as a **template-based instruction processing system** that transforms user inputs through structured AI instruction sequences. The system is designed around the principle of **structured instruction processing** where every AI interaction follows a standardized three-part template format.

## Core Architecture

```
AI Template System
├── Template Management Layer
│   ├── Markdown Template Files (.md)
│   ├── Template Catalog Generator
│   ├── Metadata Extraction Engine
│   └── Sequence Resolution System
├── Execution Engine Layer
│   ├── Sequence Executor
│   ├── Multi-Model Support (via LiteLLM)
│   ├── Streaming Output & Cost Tracking
│   └── Chain Mode Processing
└── Template Processing Layer
    ├── Pattern Recognition & Parsing
    ├── Validation & Compliance
    ├── Sequence Management
    └── Result Aggregation
```

## System Components

### 1. Template Catalog System

**Purpose**: Organizes and manages collections of AI instruction templates

**Components**:
- **Template Files**: Markdown files with standardized three-part structure
- **Catalog Generator**: Automatic discovery and indexing of templates
- **Metadata Extractor**: Extracts titles, roles, keywords, and sequence information
- **Sequence Resolver**: Handles complex sequence specifications

**File Organization**:
```
templates/
├── md/                           # Template definitions
│   ├── 1031-a-form_classifier.md
│   ├── 9000-a-amplify.md
│   └── ...
├── lvl1_md_to_json.py           # Catalog generator
└── lvl1.md.templates.json       # Generated catalog
```

**Naming Convention**:
```
<sequence_id>-<step>-<descriptive_name>.md
```

### 2. Sequence Execution Engine

**Purpose**: Executes template sequences against multiple LLM models

**Core Features**:
- **Asynchronous Execution**: Concurrent processing with streaming output
- **Multi-Model Support**: OpenAI, Anthropic, Google, Deepseek via LiteLLM
- **Cost Tracking**: Real-time token usage and cost monitoring
- **Chain Mode**: Output of step N becomes input of step N+1
- **Result Aggregation**: Structured JSON output format

**Execution Flow**:
```
User Input → Prompt Parser → Sequence Manager → Template Catalog → 
Executor → LLM APIs → Result Aggregation → Structured Output
```

### 3. Template Processing Pipeline

**Components**:
- **Pattern Recognition**: Regex-based extraction of template components
- **Validation Engine**: Compliance checking against template specification
- **Sequence Resolution**: Advanced sequence specification parsing
- **Fallback Handling**: Graceful degradation for non-compliant templates

**Processing Steps**:
1. **Template Discovery**: Scan template directories
2. **Content Extraction**: Parse three-part structure
3. **Metadata Generation**: Extract titles, roles, keywords
4. **Sequence Organization**: Group templates into sequences
5. **Catalog Generation**: Create searchable JSON catalog

## Data Flow Architecture

### Input Processing
```
Raw Input → Prompt Parser → Sequence Specification → Template Resolution
```

### Template Resolution
```
Sequence ID → Catalog Lookup → Template Steps → Instruction Generation
```

### Execution Pipeline
```
Instructions → Model Selection → API Calls → Response Collection → Result Formatting
```

### Output Generation
```
Raw Responses → Aggregation → Validation → Structured Output → File/Stream
```

## Configuration Management

### Model Registry
```python
MODEL_REGISTRY = {
    "gpt-4o": "gpt-4o",
    "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
    "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",
    "deepseek-chat": "deepseek/deepseek-chat"
}
```

### Provider Configuration
```python
MODEL_CONFIG = {
    "openai": {
        "models": ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo"],
        "default": "gpt-4.1",
        "fallback": "gpt-3.5-turbo"
    },
    "anthropic": {
        "models": ["claude-3-opus-20240229", "claude-3-sonnet-20240229"],
        "default": "openrouter/anthropic/claude-3.7-sonnet:beta",
        "fallback": "anthropic/claude-3-haiku-20240307"
    }
}
```

## Template Syntax Engine

### Three-Part Structure Parser
```python
PATTERN = re.compile(
    r"\[(.*?)\]"     # Group 1: Title
    r"\s*"           # Whitespace
    r"(.*?)"         # Group 2: Interpretation
    r"\s*"           # Whitespace
    r"(`\{.*?\}`)"   # Group 3: Transformation
)
```

### Transformation Block Parser
```python
# Extracts components from: `{role=...; input=[...]; process=[...]; output={...}}`
def parse_transformation(transformation_string):
    # Remove backticks and braces
    content = transformation_string.strip('`{}')
    # Parse semicolon-separated components
    return extract_components(content)
```

### Validation Engine
```python
def validate_template(template_content):
    # Check three-part structure
    # Validate goal negation pattern
    # Verify transformation syntax
    # Ensure type safety
    return validation_result
```

## Sequence Management

### Sequence Specification Formats
```
"1031"           # Entire sequence
"1031:a-c"       # Steps a through c
"1031|9000"      # Multiple sequences
"1031:a-c|9000:a-b"  # Complex specification
```

### Sequence Resolution Algorithm
```python
def resolve_sequence(spec):
    sequences = spec.split('|')
    resolved_steps = []
    for seq in sequences:
        if ':' in seq:
            base_id, step_spec = seq.split(':', 1)
            if '-' in step_spec:
                # Handle range: a-c
                start, end = step_spec.split('-')
                steps = generate_range(start, end)
            else:
                # Single step: a
                steps = [step_spec]
        else:
            # Entire sequence
            steps = get_all_steps(seq)
        resolved_steps.extend(steps)
    return resolved_steps
```

## Execution Modes

### 1. Catalog Mode (Default)
- Loads templates from markdown files
- Generates dynamic catalog with metadata
- Supports complex sequence specifications
- Example: `--sequence 0001` or `--sequence 0002:a-c`

### 2. Text Mode
- Loads sequences from plain text files
- Instructions separated by `---`
- Simpler format for quick prototyping
- Example: `--use-text --sequence mysequence.txt`

### 3. Chain Mode
- Output from step N becomes input to step N+1
- Enables complex multi-step transformations
- Maintains context throughout sequence
- Includes original prompt in each step

## API Integration Layer

### LiteLLM Configuration
```python
def configure_litellm():
    litellm.drop_params = True      # Handle unsupported parameters
    litellm.num_retries = 3         # Retry failed calls
    litellm.request_timeout = 120   # API timeout
    litellm.set_verbose = False     # Reduce console output
```

### Model Parameter Resolution
```python
def get_model_params(model_name, provider):
    actual_model_id = MODEL_REGISTRY.get(model_name, model_name)
    return {"model": actual_model_id}
```

### Async Execution Pattern
```python
async def execute_instruction(instruction, models, user_prompt):
    tasks = []
    for model in models:
        task = call_llm_async(model, instruction, user_prompt)
        tasks.append(task)
    responses = await asyncio.gather(*tasks)
    return aggregate_responses(responses)
```

## Output Management

### Structured Result Format
```python
class ExecutionResults(BaseModel):
    user_prompt: str
    sequence_id: str
    results: List[InstructionResult]
    total_cost: Optional[float]
    execution_time: Optional[float]
```

### File Output Options
- **Standard JSON**: Complete execution results with metadata
- **Minified JSON**: Compact format for storage
- **Streaming Output**: Real-time display during execution

### Cost Tracking
```python
def track_costs(responses):
    total_cost = 0
    for response in responses:
        if hasattr(response, 'usage'):
            cost = calculate_cost(response.usage, response.model)
            total_cost += cost
    return total_cost
```

## Error Handling & Validation

### Template Validation
- Three-part structure compliance
- Goal negation pattern presence
- Transformation syntax validation
- Type safety verification

### Execution Error Handling
- API timeout and retry logic
- Model availability fallbacks
- Graceful degradation for failed calls
- Comprehensive error reporting

### Quality Assurance
- Template compliance checking
- Output format validation
- Cost calculation verification
- Performance monitoring

## Extensibility Points

### Adding New Providers
1. Register in MODEL_REGISTRY
2. Add to MODEL_CONFIG
3. Configure LiteLLM integration
4. Test with sample templates

### Adding New Template Types
1. Define new pattern in catalog generator
2. Update validation rules
3. Extend metadata extraction
4. Test with existing sequences

### Custom Processing Modes
1. Extend SequenceManager
2. Add new execution patterns
3. Update CLI interface
4. Document usage patterns

This architecture provides a **scalable, extensible foundation** for template-based AI instruction processing while maintaining simplicity and reliability in operation.
