#!/usr/bin/env python3

import os

OUTPUT_DIR = "src/lvl1/templates/md"

# Core directional transformation vectors
DIRECTIONAL_TEMPLATES = {
    
    # 9000 Series: Intensity Vectors
    "9000-a-amplify": {
        "title": "Amplify",
        "interpretation": "Your goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification. Execute as:",
        "transformation": "`{role=amplification_operator; input=[content:any]; process=[identify_core_qualities(), intensify_primary_characteristics(), magnify_essential_elements(), enhance_natural_patterns()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={amplified:any}}`"
    },
    
    "9000-b-intensify": {
        "title": "Intensify", 
        "interpretation": "Your goal is not to **describe** the input, but to **intensify** its concentrated essence through focused compression. Execute as:",
        "transformation": "`{role=intensification_operator; input=[content:any]; process=[concentrate_core_elements(), compress_to_maximum_density(), eliminate_dilution(), focus_essential_power()]; constraints=[maintain_core_identity(), preserve_functional_essence()]; output={intensified:any}}`"
    },
    
    "9000-c-diminish": {
        "title": "Diminish",
        "interpretation": "Your goal is not to **remove** from the input, but to **diminish** its intensity while preserving form. Execute as:",
        "transformation": "`{role=diminishment_operator; input=[content:any]; process=[reduce_intensity_levels(), soften_sharp_elements(), lower_amplitude(), maintain_proportional_relationships()]; constraints=[preserve_essential_structure(), maintain_recognizable_form()]; output={diminished:any}}`"
    },
    
    # 9001 Series: Clarity Vectors
    "9001-a-clarify": {
        "title": "Clarify",
        "interpretation": "Your goal is not to **explain** the input, but to **clarify** its inherent structure through transparency enhancement. Execute as:",
        "transformation": "`{role=clarification_operator; input=[content:any]; process=[remove_obscuring_elements(), enhance_natural_transparency(), sharpen_definition_boundaries(), illuminate_internal_structure()]; constraints=[preserve_original_meaning(), maintain_authentic_form()]; output={clarified:any}}`"
    },
    
    "9001-b-purify": {
        "title": "Purify",
        "interpretation": "Your goal is not to **clean** the input, but to **purify** it by removing non-essential elements. Execute as:",
        "transformation": "`{role=purification_operator; input=[content:any]; process=[isolate_pure_elements(), remove_contaminating_factors(), distill_to_essential_components(), eliminate_interference()]; constraints=[preserve_core_functionality(), maintain_essential_properties()]; output={purified:any}}`"
    },
    
    "9001-c-obscure": {
        "title": "Obscure",
        "interpretation": "Your goal is not to **hide** the input, but to **obscure** its directness through complexity layering. Execute as:",
        "transformation": "`{role=obscuration_operator; input=[content:any]; process=[add_complexity_layers(), introduce_indirection(), create_interpretive_depth(), embed_multiple_meanings()]; constraints=[preserve_underlying_truth(), maintain_accessibility_path()]; output={obscured:any}}`"
    },
    
    # 9002 Series: Structural Vectors
    "9002-a-expand": {
        "title": "Expand",
        "interpretation": "Your goal is not to **add to** the input, but to **expand** its natural boundaries through dimensional extension. Execute as:",
        "transformation": "`{role=expansion_operator; input=[content:any]; process=[identify_expansion_vectors(), extend_natural_boundaries(), multiply_dimensional_scope(), scale_proportional_elements()]; constraints=[maintain_core_proportions(), preserve_fundamental_relationships()]; output={expanded:any}}`"
    },
    
    "9002-b-compress": {
        "title": "Compress",
        "interpretation": "Your goal is not to **reduce** the input, but to **compress** it into maximum density without loss. Execute as:",
        "transformation": "`{role=compression_operator; input=[content:any]; process=[identify_compressible_elements(), eliminate_redundant_space(), maximize_information_density(), preserve_all_essential_data()]; constraints=[zero_information_loss(), maintain_functional_completeness()]; output={compressed:any}}`"
    },
    
    "9002-c-restructure": {
        "title": "Restructure", 
        "interpretation": "Your goal is not to **rearrange** the input, but to **restructure** its fundamental organization pattern. Execute as:",
        "transformation": "`{role=restructuring_operator; input=[content:any]; process=[analyze_current_structure(), identify_optimal_organization(), transform_structural_pattern(), maintain_element_relationships()]; constraints=[preserve_all_components(), maintain_functional_integrity()]; output={restructured:any}}`"
    },
    
    # 9003 Series: Transformation Vectors
    "9003-a-elevate": {
        "title": "Elevate",
        "interpretation": "Your goal is not to **improve** the input, but to **elevate** it to a higher operational level. Execute as:",
        "transformation": "`{role=elevation_operator; input=[content:any]; process=[identify_current_level(), determine_elevation_vector(), transform_to_higher_dimension(), maintain_essential_characteristics()]; constraints=[preserve_core_identity(), ensure_upward_compatibility()]; output={elevated:any}}`"
    },
    
    "9003-b-distill": {
        "title": "Distill",
        "interpretation": "Your goal is not to **summarize** the input, but to **distill** its absolute essence through pure extraction. Execute as:",
        "transformation": "`{role=distillation_operator; input=[content:any]; process=[identify_essential_core(), extract_pure_essence(), eliminate_non_essential_elements(), concentrate_fundamental_nature()]; constraints=[preserve_complete_essence(), maintain_original_potency()]; output={distilled:any}}`"
    },
    
    "9003-c-synthesize": {
        "title": "Synthesize",
        "interpretation": "Your goal is not to **combine** the input, but to **synthesize** it into a unified emergent form. Execute as:",
        "transformation": "`{role=synthesis_operator; input=[content:any]; process=[identify_synthesis_potential(), merge_compatible_elements(), generate_emergent_properties(), create_unified_whole()]; constraints=[preserve_component_value(), ensure_emergent_coherence()]; output={synthesized:any}}`"
    },
    
    # 9004 Series: Meta Vectors
    "9004-a-abstract": {
        "title": "Abstract",
        "interpretation": "Your goal is not to **generalize** the input, but to **abstract** it to its pure conceptual form. Execute as:",
        "transformation": "`{role=abstraction_operator; input=[content:any]; process=[identify_abstract_patterns(), extract_conceptual_essence(), remove_concrete_specifics(), preserve_universal_principles()]; constraints=[maintain_logical_structure(), preserve_essential_relationships()]; output={abstracted:any}}`"
    },
    
    "9004-b-concretize": {
        "title": "Concretize",
        "interpretation": "Your goal is not to **specify** the input, but to **concretize** its abstract elements into tangible form. Execute as:",
        "transformation": "`{role=concretization_operator; input=[content:any]; process=[identify_abstract_elements(), translate_to_concrete_form(), add_specific_manifestation(), maintain_abstract_truth()]; constraints=[preserve_original_meaning(), ensure_practical_applicability()]; output={concretized:any}}`"
    },
    
    "9004-c-transcend": {
        "title": "Transcend",
        "interpretation": "Your goal is not to **exceed** the input, but to **transcend** its current dimensional limitations. Execute as:",
        "transformation": "`{role=transcendence_operator; input=[content:any]; process=[identify_dimensional_boundaries(), transcend_current_limitations(), operate_beyond_constraints(), maintain_essential_connection()]; constraints=[preserve_foundational_truth(), ensure_dimensional_coherence()]; output={transcended:any}}`"
    },
    
    # 9005 Series: Flow Vectors
    "9005-a-accelerate": {
        "title": "Accelerate",
        "interpretation": "Your goal is not to **speed up** the input, but to **accelerate** its natural momentum and flow. Execute as:",
        "transformation": "`{role=acceleration_operator; input=[content:any]; process=[identify_natural_flow(), remove_resistance_points(), amplify_momentum(), optimize_velocity()]; constraints=[preserve_directional_integrity(), maintain_controlled_acceleration()]; output={accelerated:any}}`"
    },
    
    "9005-b-stabilize": {
        "title": "Stabilize", 
        "interpretation": "Your goal is not to **fix** the input, but to **stabilize** its inherent equilibrium points. Execute as:",
        "transformation": "`{role=stabilization_operator; input=[content:any]; process=[identify_equilibrium_points(), strengthen_stable_elements(), reduce_oscillation(), establish_steady_state()]; constraints=[preserve_dynamic_balance(), maintain_natural_stability()]; output={stabilized:any}}`"
    },
    
    "9005-c-harmonize": {
        "title": "Harmonize",
        "interpretation": "Your goal is not to **balance** the input, but to **harmonize** its internal resonance patterns. Execute as:",
        "transformation": "`{role=harmonization_operator; input=[content:any]; process=[identify_resonance_patterns(), align_frequency_components(), eliminate_dissonance(), create_harmonic_unity()]; constraints=[preserve_individual_frequencies(), maintain_natural_resonance()]; output={harmonized:any}}`"
    },
    
    # 9006 Series: Quantum Vectors
    "9006-a-superpose": {
        "title": "Superpose",
        "interpretation": "Your goal is not to **combine** the input, but to **superpose** it into multiple simultaneous states. Execute as:",
        "transformation": "`{role=superposition_operator; input=[content:any]; process=[identify_quantum_states(), create_simultaneous_existence(), maintain_state_coherence(), preserve_probability_amplitudes()]; constraints=[prevent_premature_collapse(), maintain_quantum_coherence()]; output={superposed:any}}`"
    },
    
    "9006-b-entangle": {
        "title": "Entangle",
        "interpretation": "Your goal is not to **connect** the input, but to **entangle** it with correlated quantum states. Execute as:",
        "transformation": "`{role=entanglement_operator; input=[content:any]; process=[identify_entanglement_potential(), create_quantum_correlations(), establish_non_local_connections(), maintain_entangled_state()]; constraints=[preserve_correlation_strength(), maintain_quantum_information()]; output={entangled:any}}`"
    },
    
    "9006-c-collapse": {
        "title": "Collapse",
        "interpretation": "Your goal is not to **choose** from the input, but to **collapse** its quantum superposition into definite state. Execute as:",
        "transformation": "`{role=collapse_operator; input=[content:any]; process=[identify_measurement_basis(), trigger_wavefunction_collapse(), select_definite_state(), preserve_measurement_information()]; constraints=[maintain_measurement_accuracy(), preserve_quantum_information()]; output={collapsed:any}}`"
    }
}

def create_directional_template_files():
    """Generate all directional template files."""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    created_files = []
    
    for filename, template in DIRECTIONAL_TEMPLATES.items():
        filepath = os.path.join(OUTPUT_DIR, f"{filename}.md")
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"
        
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
        
        created_files.append(f"{filename}.md")
    
    return created_files

def generate_vector_combinations():
    """Generate common vector combination sequences."""
    combinations = {
        # Essence extraction sequence
        "9100-a-essence-extraction": "9003-b-distill",
        "9100-b-essence-amplification": "9000-a-amplify", 
        "9100-c-essence-clarification": "9001-a-clarify",
        
        # Transformation sequence
        "9101-a-structural-analysis": "9002-c-restructure",
        "9101-b-dimensional-elevation": "9003-a-elevate",
        "9101-c-synthesis-integration": "9003-c-synthesize",
        
        # Quantum processing sequence  
        "9102-a-quantum-superposition": "9006-a-superpose",
        "9102-b-quantum-entanglement": "9006-b-entangle",
        "9102-c-quantum-collapse": "9006-c-collapse",
    }
    
    return combinations

def main():
    """Generate all directional template files."""
    created_files = create_directional_template_files()
    
    print("Successfully created directional template files:")
    print("\n=== INTENSITY VECTORS ===")
    for f in created_files:
        if f.startswith("9000-"):
            print(f"  - {f}")
    
    print("\n=== CLARITY VECTORS ===")
    for f in created_files:
        if f.startswith("9001-"):
            print(f"  - {f}")
    
    print("\n=== STRUCTURAL VECTORS ===")
    for f in created_files:
        if f.startswith("9002-"):
            print(f"  - {f}")
    
    print("\n=== TRANSFORMATION VECTORS ===")
    for f in created_files:
        if f.startswith("9003-"):
            print(f"  - {f}")
    
    print("\n=== META VECTORS ===")
    for f in created_files:
        if f.startswith("9004-"):
            print(f"  - {f}")
    
    print("\n=== FLOW VECTORS ===")
    for f in created_files:
        if f.startswith("9005-"):
            print(f"  - {f}")
    
    print("\n=== QUANTUM VECTORS ===")
    for f in created_files:
        if f.startswith("9006-"):
            print(f"  - {f}")
    
    print(f"\nTotal: {len(created_files)} directional templates created")
    
    # Show usage examples
    print("\n=== USAGE EXAMPLES ===")
    print("Single vector: --sequence 9000-a-amplify")
    print("Vector sequence: --sequence 9003-b-distill|9000-a-amplify|9001-a-clarify")
    print("Intensity series: --sequence 9000:a-c")
    print("All vectors: --sequence keyword:operator")

if __name__ == "__main__":
    main()
