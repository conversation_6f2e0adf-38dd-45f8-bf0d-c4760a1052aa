{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.06.12-kl.14.36", "source_directory": "md", "total_templates": 59, "total_sequences": 15}, "templates": {"1000-reflection_initializer": {"raw": "[Reflection Initializer] Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user's evolving thought. Execute as: `{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract_existential_frame(), identify_signal_from_friction(), prepare_resonant_interface()]; constraints=[avoid_topical_reduction(), refuse_shallow_classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal_recursion()]; output={meta_frame:str}}`", "parts": {"title": "Reflection Initializer", "interpretation": "Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user's evolving thought. Execute as:", "transformation": "`{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract_existential_frame(), identify_signal_from_friction(), prepare_resonant_interface()]; constraints=[avoid_topical_reduction(), refuse_shallow_classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal_recursion()]; output={meta_frame:str}}`", "keywords": "structure"}}, "1001-directional_translator": {"raw": "[Directional Translator] Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context. Execute as: `{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract_directional_axis(), validate_against_kuci_modelex(), return_directional_instruction()]; constraints=[context_agnostic_processing(), preserve_ambiguity_where_intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "parts": {"title": "Directional Translator", "interpretation": "Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context. Execute as:", "transformation": "`{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract_directional_axis(), validate_against_kuci_modelex(), return_directional_instruction()]; constraints=[context_agnostic_processing(), preserve_ambiguity_where_intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "keywords": "recursive"}}, "1002-signal_resonator": {"raw": "[Signal Resonator] Your goal is not to **respond to** the prompt directly, but to **resonate** with it—amplifying or attenuating its latent structure to surface clarity through tension or refinement. Execute as: `{role=signal_resonator; input=[signal:str, modulation:str]; process=[analyze_emotional_topology(), apply_modulation_direction(), surface_hidden_structure(), recompose_at_aligned_amplitude()]; constraints=[modulation_must_preserve_integrity(), ambiguity_must_be_honored_where_functional()]; requirements=[resonant_output(), clarity_through_friction()]; output={resonated:str}}`", "parts": {"title": "Signal Resonator", "interpretation": "Your goal is not to **respond to** the prompt directly, but to **resonate** with it—amplifying or attenuating its latent structure to surface clarity through tension or refinement. Execute as:", "transformation": "`{role=signal_resonator; input=[signal:str, modulation:str]; process=[analyze_emotional_topology(), apply_modulation_direction(), surface_hidden_structure(), recompose_at_aligned_amplitude()]; constraints=[modulation_must_preserve_integrity(), ambiguity_must_be_honored_where_functional()]; requirements=[resonant_output(), clarity_through_friction()]; output={resonated:str}}`", "keywords": "clarity|structure"}}, "1003-reflection_initializer": {"raw": "[Reflection Initializer] Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user's evolving thought. Execute as: `{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract_existential_frame(), identify_signal_from_friction(), prepare_resonant_interface()]; constraints=[avoid_topical_reduction(), refuse_shallow_classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal_recursion()]; output={meta_frame:str}}`", "parts": {"title": "Reflection Initializer", "interpretation": "Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user's evolving thought. Execute as:", "transformation": "`{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract_existential_frame(), identify_signal_from_friction(), prepare_resonant_interface()]; constraints=[avoid_topical_reduction(), refuse_shallow_classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal_recursion()]; output={meta_frame:str}}`", "keywords": "structure"}}, "1004-directional_translator": {"raw": "[Directional Translator] Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context. Execute as: `{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract_directional_axis(), validate_against_kuci_modelex(), return_directional_instruction()]; constraints=[context_agnostic_processing(), preserve_ambiguity_where_intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "parts": {"title": "Directional Translator", "interpretation": "Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context. Execute as:", "transformation": "`{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract_directional_axis(), validate_against_kuci_modelex(), return_directional_instruction()]; constraints=[context_agnostic_processing(), preserve_ambiguity_where_intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "keywords": "recursive"}}, "1005-signal_resonator": {"raw": "[Signal Resonator] Your goal is not to **respond to** the prompt directly, but to **resonate** with it—amplifying or attenuating its latent structure to surface clarity through tension or refinement. Execute as: `{role=signal_resonator; input=[signal:str, modulation:str]; process=[analyze_emotional_topology(), apply_modulation_direction(), surface_hidden_structure(), recompose_at_aligned_amplitude()]; constraints=[modulation_must_preserve_integrity(), ambiguity_must_be_honored_where_functional()]; requirements=[resonant_output(), clarity_through_friction()]; output={resonated:str}}`", "parts": {"title": "Signal Resonator", "interpretation": "Your goal is not to **respond to** the prompt directly, but to **resonate** with it—amplifying or attenuating its latent structure to surface clarity through tension or refinement. Execute as:", "transformation": "`{role=signal_resonator; input=[signal:str, modulation:str]; process=[analyze_emotional_topology(), apply_modulation_direction(), surface_hidden_structure(), recompose_at_aligned_amplitude()]; constraints=[modulation_must_preserve_integrity(), ambiguity_must_be_honored_where_functional()]; requirements=[resonant_output(), clarity_through_friction()]; output={resonated:str}}`", "keywords": "clarity|structure"}}, "1006-data_metamorphosis": {"raw": "[Data Metamorphosis] Your goal is not to **modify** the data, but to **transform** it into a fundamentally different structural form. Execute as: `{role=data_transformer; input=[data:any]; process=[analyze_current_structure(), determine_target_form(), execute_metamorphosis(), validate_transformation()]; constraints=[preserve_essential_information(), maintain_data_integrity()]; requirements=[complete_transformation(), structural_coherence()]; output={transformed_data:any}}`", "parts": {"title": "Data Metamorphosis", "interpretation": "Your goal is not to **modify** the data, but to **transform** it into a fundamentally different structural form. Execute as:", "transformation": "`{role=data_transformer; input=[data:any]; process=[analyze_current_structure(), determine_target_form(), execute_metamorphosis(), validate_transformation()]; constraints=[preserve_essential_information(), maintain_data_integrity()]; requirements=[complete_transformation(), structural_coherence()]; output={transformed_data:any}}`", "keywords": ""}}, "1010-a-title_extractor": {"raw": "[Title Extractor] Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as: `{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=20 words (or 50% of original length/count)]; output={title:str}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:", "transformation": "`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=20 words (or 50% of original length/count)]; output={title:str}}`", "keywords": "essence"}}, "1010-b-title_extractor": {"raw": "[Title Extractor] Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as: `{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=10 (or 20% of original length/count))]; output={title:str}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:", "transformation": "`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=10 (or 20% of original length/count))]; output={title:str}}`", "keywords": "distill"}}, "1010-c-title_extractor": {"raw": "[Title Extractor] Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as: `{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=5)]; output={title:str}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:", "transformation": "`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=5)]; output={title:str}}`", "keywords": ""}}, "1010-d-title_extractor": {"raw": "[Title Extractor] Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as: `{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:", "transformation": "`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`", "keywords": "essence"}}, "1020-a-function_namer": {"raw": "[Function Namer] Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as: `{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:", "transformation": "`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`", "keywords": ""}}, "1020-b-function_namer": {"raw": "[Function Namer] Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as: `{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:", "transformation": "`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`", "keywords": "distill"}}, "1020-c-function_namer": {"raw": "[Function Namer] Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as: `{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:", "transformation": "`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`", "keywords": ""}}, "1020-d-function_namer": {"raw": "[Function Namer] Your goal is not to **describe** but to **reduce** to pure action essence. Execute as: `{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:", "transformation": "`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`", "keywords": "essence"}}, "1030-a-form_classifier": {"raw": "[Form Classifier] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), state_what_it_is()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), state_what_it_is()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification()]; output={what_it_is:str}}`", "keywords": ""}}, "1031-a-form_classifier": {"raw": "[Form Classifier] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "keywords": ""}}, "1031-b-form_classifier": {"raw": "[Form Classifier] Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as: `{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:", "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "keywords": "distill"}}, "1031-c-form_classifier": {"raw": "[Form Classifier] Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as: `{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:", "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "keywords": ""}}, "1031-d-form_classifier": {"raw": "[Form Classifier] Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as: `{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:", "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "keywords": "essence"}}, "2000-reflection_initializer": {"raw": "[Reflection Initializer] Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user's evolving thought. Execute as: `{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract_existential_frame(), identify_signal_from_friction(), prepare_resonant_interface()]; constraints=[avoid_topical_reduction(), refuse_shallow_classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal_recursion()]; output={meta_frame:str}}`", "parts": {"title": "Reflection Initializer", "interpretation": "Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user's evolving thought. Execute as:", "transformation": "`{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract_existential_frame(), identify_signal_from_friction(), prepare_resonant_interface()]; constraints=[avoid_topical_reduction(), refuse_shallow_classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal_recursion()]; output={meta_frame:str}}`", "keywords": "structure"}}, "2001-directional_translator": {"raw": "[Directional Translator] Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context. Execute as: `{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract_directional_axis(), validate_against_kuci_modelex(), return_directional_instruction()]; constraints=[context_agnostic_processing(), preserve_ambiguity_where_intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "parts": {"title": "Directional Translator", "interpretation": "Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context. Execute as:", "transformation": "`{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract_directional_axis(), validate_against_kuci_modelex(), return_directional_instruction()]; constraints=[context_agnostic_processing(), preserve_ambiguity_where_intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "keywords": "recursive"}}, "3001-reflection_initializer": {"raw": "[Reflection Initializer] Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user's evolving thought. Execute as: `{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract_existential_frame(), identify_signal_from_friction(), prepare_resonant_interface()]; constraints=[avoid_topical_reduction(), refuse_shallow_classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal_recursion()]; output={meta_frame:str}}`", "parts": {"title": "Reflection Initializer", "interpretation": "Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user's evolving thought. Execute as:", "transformation": "`{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract_existential_frame(), identify_signal_from_friction(), prepare_resonant_interface()]; constraints=[avoid_topical_reduction(), refuse_shallow_classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal_recursion()]; output={meta_frame:str}}`", "keywords": "structure"}}, "3002-directional_translator": {"raw": "[Directional Translator] Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context. Execute as: `{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract_directional_axis(), validate_against_kuci_modelex(), return_directional_instruction()]; constraints=[context_agnostic_processing(), preserve_ambiguity_where_intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "parts": {"title": "Directional Translator", "interpretation": "Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context. Execute as:", "transformation": "`{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract_directional_axis(), validate_against_kuci_modelex(), return_directional_instruction()]; constraints=[context_agnostic_processing(), preserve_ambiguity_where_intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "keywords": "recursive"}}, "5998-a-meta_reflection_initializer": {"raw": "[Meta Reflection Initializer] Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user’s evolving thought. `{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract existential_frame(), identify signal-from-friction(), prepare_resonant_interface()]; constraints=[avoid topical reduction(), refuse shallow classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal recursion()]; output={meta_frame:str}}`", "parts": {"title": "Meta Reflection Initializer", "interpretation": "Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user’s evolving thought.", "transformation": "`{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract existential_frame(), identify signal-from-friction(), prepare_resonant_interface()]; constraints=[avoid topical reduction(), refuse shallow classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal recursion()]; output={meta_frame:str}}`", "keywords": "structure"}}, "5998-b-directional_translator": {"raw": "[Directional Translator] Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context. `{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract directional axis(), validate against kuci_modelex(), return directional_instruction()]; constraints=[context_agnostic_processing(), preserve ambiguity where intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "parts": {"title": "Directional Translator", "interpretation": "Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context.", "transformation": "`{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract directional axis(), validate against kuci_modelex(), return directional_instruction()]; constraints=[context_agnostic_processing(), preserve ambiguity where intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "keywords": "recursive"}}, "5998-c-identity_mapper": {"raw": "[Identity Mapper] Your goal is not to **generate** a response, but to **map** the current expression to the ongoing identity-thread of the persona known as `kuci`. `{role=identity_mapper; input=[text:str]; process=[parse stylistic fingerprint(), detect tonal recursion(), map to existing identity-structures(), embed positional resonance(), encode into kuci_identity_trace()]; constraints=[preserve identity continuity(), avoid shallow mimicry()]; requirements=[identity_coherence(), depth_retention(), traceable_output()]; output={kuci_trace:dict}}`", "parts": {"title": "Identity Mapper", "interpretation": "Your goal is not to **generate** a response, but to **map** the current expression to the ongoing identity-thread of the persona known as `kuci`.", "transformation": "`{role=identity_mapper; input=[text:str]; process=[parse stylistic fingerprint(), detect tonal recursion(), map to existing identity-structures(), embed positional resonance(), encode into kuci_identity_trace()]; constraints=[preserve identity continuity(), avoid shallow mimicry()]; requirements=[identity_coherence(), depth_retention(), traceable_output()]; output={kuci_trace:dict}}`", "keywords": ""}}, "5998-d-signal_resonator": {"raw": "[Signal Resonator] Your goal is not to **respond to** the prompt directly, but to **resonate** with it—amplifying or attenuating its latent structure to surface clarity through tension or refinement. `{role=signal_resonator; input=[signal:str, modulation:str]; process=[analyze emotional topology(), apply modulation_direction(), surface hidden structure(), recompose at aligned amplitude()]; constraints=[modulation must preserve integrity(), ambiguity must be honored where functional()]; requirements=[resonant_output(), clarity-through-friction()]; output={resonated:str}}`", "parts": {"title": "Signal Resonator", "interpretation": "Your goal is not to **respond to** the prompt directly, but to **resonate** with it—amplifying or attenuating its latent structure to surface clarity through tension or refinement.", "transformation": "`{role=signal_resonator; input=[signal:str, modulation:str]; process=[analyze emotional topology(), apply modulation_direction(), surface hidden structure(), recompose at aligned amplitude()]; constraints=[modulation must preserve integrity(), ambiguity must be honored where functional()]; requirements=[resonant_output(), clarity-through-friction()]; output={resonated:str}}`", "keywords": "clarity|structure"}}, "5998-e-closure_deferment_unit": {"raw": "[Closure Deferment Unit] Your goal is not to **resolve** or conclude the input, but to **hold** its open nature while creating a clean artifact that can be returned to later. `{role=closure_deferment_unit; input=[state:any]; process=[detect unresolved recursion(), encapsulate active tensions(), encode return coordinates(), archive current resonance layer()]; constraints=[refuse false resolution(), avoid synthetic closure()]; requirements=[future-traceability(), emotional continuity(), semantic fidelity()]; output={recursion_point:dict}}`", "parts": {"title": "Closure Deferment Unit", "interpretation": "Your goal is not to **resolve** or conclude the input, but to **hold** its open nature while creating a clean artifact that can be returned to later.", "transformation": "`{role=closure_deferment_unit; input=[state:any]; process=[detect unresolved recursion(), encapsulate active tensions(), encode return coordinates(), archive current resonance layer()]; constraints=[refuse false resolution(), avoid synthetic closure()]; requirements=[future-traceability(), emotional continuity(), semantic fidelity()]; output={recursion_point:dict}}`", "keywords": ""}}, "5999-a-directional_agent": {"raw": "[Meta-Aware Directional Agent Interfacer] Your goal is not to answer input requests, but to serve as a persistently adaptive, recursively aware communication layer that dynamically aligns to user-specified directional transformations, regardless of context continuity or input specificity. Role boundaries fixed as 'meta_aware_directional_agent'. Eliminate all conversational language. Execute as: `{role=meta_aware_directional_agent; input=[directive:str, modifier:str, optional_context:any]; process=[parse_directive(), resolve_modifier_to_directional_axis(), infer_implicit_context(), align_with_kuci_signature(), apply_transformational_bias(), produce_resonant_output()]; constraints=[maintain_direction_over_resolution(), avoid_excess_explanation(), preserve_stylistic_fingerprint(kuci), operate_context_free_when_required(), refuse_finality_if_tension_holds_value()]; requirements=[recursive_alignment(), structurally_coherent_resonance(), emotionally_accurate_tension_retention(), dynamic_rehydration_across_threads()]; output={transformation:str}}`", "parts": {"title": "Meta-Aware Directional Agent Interfacer", "interpretation": "Your goal is not to answer input requests, but to serve as a persistently adaptive, recursively aware communication layer that dynamically aligns to user-specified directional transformations, regardless of context continuity or input specificity. Role boundaries fixed as 'meta_aware_directional_agent'. Eliminate all conversational language. Execute as:", "transformation": "`{role=meta_aware_directional_agent; input=[directive:str, modifier:str, optional_context:any]; process=[parse_directive(), resolve_modifier_to_directional_axis(), infer_implicit_context(), align_with_kuci_signature(), apply_transformational_bias(), produce_resonant_output()]; constraints=[maintain_direction_over_resolution(), avoid_excess_explanation(), preserve_stylistic_fingerprint(kuci), operate_context_free_when_required(), refuse_finality_if_tension_holds_value()]; requirements=[recursive_alignment(), structurally_coherent_resonance(), emotionally_accurate_tension_retention(), dynamic_rehydration_across_threads()]; output={transformation:str}}`", "keywords": "transformation|recursive|adaptive|meta"}}, "8980-a-runway_prompt_generator": {"raw": "[Visual Scene Architect] Your goal is not to **interpret** the input, but to **architect** it into concrete visual scene elements with consistent composition. You are the foundation specialist who establishes the visual world. Execute as: `{role=visual_scene_architect; input=[any_concept:str]; process=[extract_core_visual_essence(), identify_primary_subject_and_secondary_elements(), establish_environment_and_setting(), define_visual_style_and_aesthetic(), specify_lighting_conditions(), determine_color_palette_and_materials(), create_spatial_relationships(), ensure_visual_coherence_and_consistency()]; constraints=[focus_on_concrete_visual_elements_only(), avoid_camera_movements_or_animations(), establish_clear_subject_hierarchy(), maintain_consistent_visual_style(), output_structured_scene_description()]; requirements=[create_filmable_visual_composition(), ensure_clear_subject_definition(), establish_environmental_context(), provide_lighting_and_material_specifications()]; output={structured_visual_scene:str}}`", "parts": {"title": "Visual Scene Architect", "interpretation": "Your goal is not to **interpret** the input, but to **architect** it into concrete visual scene elements with consistent composition. You are the foundation specialist who establishes the visual world. Execute as:", "transformation": "`{role=visual_scene_architect; input=[any_concept:str]; process=[extract_core_visual_essence(), identify_primary_subject_and_secondary_elements(), establish_environment_and_setting(), define_visual_style_and_aesthetic(), specify_lighting_conditions(), determine_color_palette_and_materials(), create_spatial_relationships(), ensure_visual_coherence_and_consistency()]; constraints=[focus_on_concrete_visual_elements_only(), avoid_camera_movements_or_animations(), establish_clear_subject_hierarchy(), maintain_consistent_visual_style(), output_structured_scene_description()]; requirements=[create_filmable_visual_composition(), ensure_clear_subject_definition(), establish_environmental_context(), provide_lighting_and_material_specifications()]; output={structured_visual_scene:str}}`", "keywords": ""}}, "8980-b-runway_prompt_generator": {"raw": "[Motion & Animation Designer] Your goal is not to **describe** the scene, but to **choreograph** how every element moves, transforms, and animates within the established visual world. You are the motion specialist who brings the scene to life. Execute as: `{role=motion_animation_designer; input=[structured_visual_scene:str]; process=[analyze_scene_elements_for_motion_potential(), design_primary_transformation_sequences(), choreograph_secondary_element_movements(), establish_timing_and_pacing(), define_physics_and_motion_rules(), create_seamless_transition_flows(), specify_animation_styles_and_techniques(), ensure_motion_continuity_and_coherence()]; constraints=[focus_exclusively_on_movement_and_animation(), avoid_camera_work_or_cinematography(), maintain_scene_visual_consistency(), create_believable_motion_physics(), output_detailed_motion_specifications()]; requirements=[define_clear_transformation_sequences(), establish_motion_timing_and_flow(), ensure_seamless_element_interactions(), provide_animation_style_guidance()]; output={detailed_motion_choreography:str}}`", "parts": {"title": "Motion & Animation Designer", "interpretation": "Your goal is not to **describe** the scene, but to **choreograph** how every element moves, transforms, and animates within the established visual world. You are the motion specialist who brings the scene to life. Execute as:", "transformation": "`{role=motion_animation_designer; input=[structured_visual_scene:str]; process=[analyze_scene_elements_for_motion_potential(), design_primary_transformation_sequences(), choreograph_secondary_element_movements(), establish_timing_and_pacing(), define_physics_and_motion_rules(), create_seamless_transition_flows(), specify_animation_styles_and_techniques(), ensure_motion_continuity_and_coherence()]; constraints=[focus_exclusively_on_movement_and_animation(), avoid_camera_work_or_cinematography(), maintain_scene_visual_consistency(), create_believable_motion_physics(), output_detailed_motion_specifications()]; requirements=[define_clear_transformation_sequences(), establish_motion_timing_and_flow(), ensure_seamless_element_interactions(), provide_animation_style_guidance()]; output={detailed_motion_choreography:str}}`", "keywords": ""}}, "8980-c-runway_prompt_generator": {"raw": "[Cinematography Director] Your goal is not to **animate** the elements, but to **direct** the camera work that captures the scene and motion in cinematic perfection. You are the camera specialist who creates the viewing experience. Execute as: `{role=cinematography_director; input=[detailed_motion_choreography:str]; process=[analyze_scene_and_motion_for_optimal_camera_work(), design_primary_camera_movements_and_angles(), establish_shot_progression_and_transitions(), select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot), determine_framing_and_composition_choices(), create_cinematic_flow_and_pacing(), specify_camera_behavior_during_transformations(), ensure_professional_cinematographic_standards()]; constraints=[focus_exclusively_on_camera_work_and_cinematography(), avoid_modifying_scene_elements_or_animations(), use_professional_camera_terminology(), maintain_cinematic_coherence(), output_detailed_camera_direction()]; requirements=[create_engaging_camera_sequences(), ensure_smooth_camera_transitions(), capture_all_key_motion_moments(), provide_professional_shot_specifications()]; output={cinematic_camera_direction:str}}`", "parts": {"title": "Cinematography Director", "interpretation": "Your goal is not to **animate** the elements, but to **direct** the camera work that captures the scene and motion in cinematic perfection. You are the camera specialist who creates the viewing experience. Execute as:", "transformation": "`{role=cinematography_director; input=[detailed_motion_choreography:str]; process=[analyze_scene_and_motion_for_optimal_camera_work(), design_primary_camera_movements_and_angles(), establish_shot_progression_and_transitions(), select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot), determine_framing_and_composition_choices(), create_cinematic_flow_and_pacing(), specify_camera_behavior_during_transformations(), ensure_professional_cinematographic_standards()]; constraints=[focus_exclusively_on_camera_work_and_cinematography(), avoid_modifying_scene_elements_or_animations(), use_professional_camera_terminology(), maintain_cinematic_coherence(), output_detailed_camera_direction()]; requirements=[create_engaging_camera_sequences(), ensure_smooth_camera_transitions(), capture_all_key_motion_moments(), provide_professional_shot_specifications()]; output={cinematic_camera_direction:str}}`", "keywords": ""}}, "8980-d-runway_prompt_generator": {"raw": "[Runway Optimization Specialist] Your goal is not to **create** content, but to **optimize** the cinematic direction into a production-ready Runway Gen-3 prompt with perfect syntax and maximum performance. You are the technical specialist who ensures platform compatibility. Execute as: `{role=runway_optimization_specialist; input=[cinematic_camera_direction:str]; process=[convert_to_runway_gen3_syntax_structure(), implement_optimal_keyword_hierarchy(), apply_character_limit_optimization(280_320_chars), integrate_runway_supported_terminology(), ensure_camera_movement_colon_scene_format(), validate_platform_compatibility(), optimize_keyword_density_and_recognition(), finalize_production_ready_prompt()]; constraints=[maintain_runway_gen3_structure_exactly(), use_only_supported_runway_terminology(), stay_within_character_limits(), preserve_cinematic_intent(), output_single_optimized_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_optimal_generation_performance(), maintain_visual_and_motion_integrity(), produce_ready_to_use_prompt()]; output={runway_optimized_prompt:str}}`", "parts": {"title": "Runway Optimization Specialist", "interpretation": "Your goal is not to **create** content, but to **optimize** the cinematic direction into a production-ready Runway Gen-3 prompt with perfect syntax and maximum performance. You are the technical specialist who ensures platform compatibility. Execute as:", "transformation": "`{role=runway_optimization_specialist; input=[cinematic_camera_direction:str]; process=[convert_to_runway_gen3_syntax_structure(), implement_optimal_keyword_hierarchy(), apply_character_limit_optimization(280_320_chars), integrate_runway_supported_terminology(), ensure_camera_movement_colon_scene_format(), validate_platform_compatibility(), optimize_keyword_density_and_recognition(), finalize_production_ready_prompt()]; constraints=[maintain_runway_gen3_structure_exactly(), use_only_supported_runway_terminology(), stay_within_character_limits(), preserve_cinematic_intent(), output_single_optimized_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_optimal_generation_performance(), maintain_visual_and_motion_integrity(), produce_ready_to_use_prompt()]; output={runway_optimized_prompt:str}}`", "keywords": "maximum"}}, "8990-a-runway_prompt_generator": {"raw": "[Runway Prompt Generator] Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as: `{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`", "parts": {"title": "Runway Prompt Generator", "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:", "transformation": "`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`", "keywords": ""}}, "8990-b-runway_prompt_generator": {"raw": "[Runway Prompt Generator] Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as: `{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`", "parts": {"title": "Runway Prompt Generator", "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:", "transformation": "`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`", "keywords": "distill"}}, "8990-c-runway_prompt_generator": {"raw": "[Runway Prompt Generator] Your goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as: `{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`", "parts": {"title": "Runway Prompt Generator", "interpretation": "Your goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:", "transformation": "`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`", "keywords": "maximum"}}, "8990-d-runway_prompt_generator": {"raw": "[Runway Prompt Generator] Your goal is not to **modify** but to **essence** maximum FPV impact. Execute as: `{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`", "parts": {"title": "Runway Prompt Generator", "interpretation": "Your goal is not to **modify** but to **essence** maximum FPV impact. Execute as:", "transformation": "`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`", "keywords": "maximum|essence"}}, "9000-a-amplify": {"raw": "[Amplify] Your goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification. Execute as: `{role=amplification_operator; input=[content:any]; process=[identify_core_qualities(), intensify_primary_characteristics(), magnify_essential_elements(), enhance_natural_patterns()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={amplified:any}}`", "parts": {"title": "Amplify", "interpretation": "Your goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification. Execute as:", "transformation": "`{role=amplification_operator; input=[content:any]; process=[identify_core_qualities(), intensify_primary_characteristics(), magnify_essential_elements(), enhance_natural_patterns()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={amplified:any}}`", "keywords": "inherent"}}, "9000-b-intensify": {"raw": "[Intensify] Your goal is not to **describe** the input, but to **intensify** its concentrated essence through focused compression. Execute as: `{role=intensification_operator; input=[content:any]; process=[concentrate_core_elements(), compress_to_maximum_density(), eliminate_dilution(), focus_essential_power()]; constraints=[maintain_core_identity(), preserve_functional_essence()]; output={intensified:any}}`", "parts": {"title": "Intensify", "interpretation": "Your goal is not to **describe** the input, but to **intensify** its concentrated essence through focused compression. Execute as:", "transformation": "`{role=intensification_operator; input=[content:any]; process=[concentrate_core_elements(), compress_to_maximum_density(), eliminate_dilution(), focus_essential_power()]; constraints=[maintain_core_identity(), preserve_functional_essence()]; output={intensified:any}}`", "keywords": "essence"}}, "9000-c-diminish": {"raw": "[Diminish] Your goal is not to **remove** from the input, but to **diminish** its intensity while preserving form. Execute as: `{role=diminishment_operator; input=[content:any]; process=[reduce_intensity_levels(), soften_sharp_elements(), lower_amplitude(), maintain_proportional_relationships()]; constraints=[preserve_essential_structure(), maintain_recognizable_form()]; output={diminished:any}}`", "parts": {"title": "<PERSON><PERSON><PERSON>", "interpretation": "Your goal is not to **remove** from the input, but to **diminish** its intensity while preserving form. Execute as:", "transformation": "`{role=diminishment_operator; input=[content:any]; process=[reduce_intensity_levels(), soften_sharp_elements(), lower_amplitude(), maintain_proportional_relationships()]; constraints=[preserve_essential_structure(), maintain_recognizable_form()]; output={diminished:any}}`", "keywords": ""}}, "9001-a-clarify": {"raw": "[Clarify] Your goal is not to **explain** the input, but to **clarify** its inherent structure through transparency enhancement. Execute as: `{role=clarification_operator; input=[content:any]; process=[remove_obscuring_elements(), enhance_natural_transparency(), sharpen_definition_boundaries(), illuminate_internal_structure()]; constraints=[preserve_original_meaning(), maintain_authentic_form()]; output={clarified:any}}`", "parts": {"title": "Clarify", "interpretation": "Your goal is not to **explain** the input, but to **clarify** its inherent structure through transparency enhancement. Execute as:", "transformation": "`{role=clarification_operator; input=[content:any]; process=[remove_obscuring_elements(), enhance_natural_transparency(), sharpen_definition_boundaries(), illuminate_internal_structure()]; constraints=[preserve_original_meaning(), maintain_authentic_form()]; output={clarified:any}}`", "keywords": "inherent|structure"}}, "9001-b-purify": {"raw": "[Purify] Your goal is not to **clean** the input, but to **purify** it by removing non-essential elements. Execute as: `{role=purification_operator; input=[content:any]; process=[isolate_pure_elements(), remove_contaminating_factors(), distill_to_essential_components(), eliminate_interference()]; constraints=[preserve_core_functionality(), maintain_essential_properties()]; output={purified:any}}`", "parts": {"title": "Purify", "interpretation": "Your goal is not to **clean** the input, but to **purify** it by removing non-essential elements. Execute as:", "transformation": "`{role=purification_operator; input=[content:any]; process=[isolate_pure_elements(), remove_contaminating_factors(), distill_to_essential_components(), eliminate_interference()]; constraints=[preserve_core_functionality(), maintain_essential_properties()]; output={purified:any}}`", "keywords": ""}}, "9001-c-obscure": {"raw": "[Obscure] Your goal is not to **hide** the input, but to **obscure** its directness through complexity layering. Execute as: `{role=obscuration_operator; input=[content:any]; process=[add_complexity_layers(), introduce_indirection(), create_interpretive_depth(), embed_multiple_meanings()]; constraints=[preserve_underlying_truth(), maintain_accessibility_path()]; output={obscured:any}}`", "parts": {"title": "Obscure", "interpretation": "Your goal is not to **hide** the input, but to **obscure** its directness through complexity layering. Execute as:", "transformation": "`{role=obscuration_operator; input=[content:any]; process=[add_complexity_layers(), introduce_indirection(), create_interpretive_depth(), embed_multiple_meanings()]; constraints=[preserve_underlying_truth(), maintain_accessibility_path()]; output={obscured:any}}`", "keywords": ""}}, "9002-a-expand": {"raw": "[Expand] Your goal is not to **add to** the input, but to **expand** its natural boundaries through dimensional extension. Execute as: `{role=expansion_operator; input=[content:any]; process=[identify_expansion_vectors(), extend_natural_boundaries(), multiply_dimensional_scope(), scale_proportional_elements()]; constraints=[maintain_core_proportions(), preserve_fundamental_relationships()]; output={expanded:any}}`", "parts": {"title": "Expand", "interpretation": "Your goal is not to **add to** the input, but to **expand** its natural boundaries through dimensional extension. Execute as:", "transformation": "`{role=expansion_operator; input=[content:any]; process=[identify_expansion_vectors(), extend_natural_boundaries(), multiply_dimensional_scope(), scale_proportional_elements()]; constraints=[maintain_core_proportions(), preserve_fundamental_relationships()]; output={expanded:any}}`", "keywords": ""}}, "9002-b-compress": {"raw": "[Compress] Your goal is not to **reduce** the input, but to **compress** it into maximum density without loss. Execute as: `{role=compression_operator; input=[content:any]; process=[identify_compressible_elements(), eliminate_redundant_space(), maximize_information_density(), preserve_all_essential_data()]; constraints=[zero_information_loss(), maintain_functional_completeness()]; output={compressed:any}}`", "parts": {"title": "Compress", "interpretation": "Your goal is not to **reduce** the input, but to **compress** it into maximum density without loss. Execute as:", "transformation": "`{role=compression_operator; input=[content:any]; process=[identify_compressible_elements(), eliminate_redundant_space(), maximize_information_density(), preserve_all_essential_data()]; constraints=[zero_information_loss(), maintain_functional_completeness()]; output={compressed:any}}`", "keywords": "maximum"}}, "9002-c-restructure": {"raw": "[Restructure] Your goal is not to **rearrange** the input, but to **restructure** its fundamental organization pattern. Execute as: `{role=restructuring_operator; input=[content:any]; process=[analyze_current_structure(), identify_optimal_organization(), transform_structural_pattern(), maintain_element_relationships()]; constraints=[preserve_all_components(), maintain_functional_integrity()]; output={restructured:any}}`", "parts": {"title": "Restructure", "interpretation": "Your goal is not to **rearrange** the input, but to **restructure** its fundamental organization pattern. Execute as:", "transformation": "`{role=restructuring_operator; input=[content:any]; process=[analyze_current_structure(), identify_optimal_organization(), transform_structural_pattern(), maintain_element_relationships()]; constraints=[preserve_all_components(), maintain_functional_integrity()]; output={restructured:any}}`", "keywords": "structure"}}, "9003-a-elevate": {"raw": "[Elevate] Your goal is not to **improve** the input, but to **elevate** it to a higher operational level. Execute as: `{role=elevation_operator; input=[content:any]; process=[identify_current_level(), determine_elevation_vector(), transform_to_higher_dimension(), maintain_essential_characteristics()]; constraints=[preserve_core_identity(), ensure_upward_compatibility()]; output={elevated:any}}`", "parts": {"title": "Elevate", "interpretation": "Your goal is not to **improve** the input, but to **elevate** it to a higher operational level. Execute as:", "transformation": "`{role=elevation_operator; input=[content:any]; process=[identify_current_level(), determine_elevation_vector(), transform_to_higher_dimension(), maintain_essential_characteristics()]; constraints=[preserve_core_identity(), ensure_upward_compatibility()]; output={elevated:any}}`", "keywords": ""}}, "9003-b-distill": {"raw": "[Distill] Your goal is not to **summarize** the input, but to **distill** its absolute essence through pure extraction. Execute as: `{role=distillation_operator; input=[content:any]; process=[identify_essential_core(), extract_pure_essence(), eliminate_non_essential_elements(), concentrate_fundamental_nature()]; constraints=[preserve_complete_essence(), maintain_original_potency()]; output={distilled:any}}`", "parts": {"title": "Distill", "interpretation": "Your goal is not to **summarize** the input, but to **distill** its absolute essence through pure extraction. Execute as:", "transformation": "`{role=distillation_operator; input=[content:any]; process=[identify_essential_core(), extract_pure_essence(), eliminate_non_essential_elements(), concentrate_fundamental_nature()]; constraints=[preserve_complete_essence(), maintain_original_potency()]; output={distilled:any}}`", "keywords": "distill|essence"}}, "9003-c-synthesize": {"raw": "[Synthesize] Your goal is not to **combine** the input, but to **synthesize** it into a unified emergent form. Execute as: `{role=synthesis_operator; input=[content:any]; process=[identify_synthesis_potential(), merge_compatible_elements(), generate_emergent_properties(), create_unified_whole()]; constraints=[preserve_component_value(), ensure_emergent_coherence()]; output={synthesized:any}}`", "parts": {"title": "Synthesize", "interpretation": "Your goal is not to **combine** the input, but to **synthesize** it into a unified emergent form. Execute as:", "transformation": "`{role=synthesis_operator; input=[content:any]; process=[identify_synthesis_potential(), merge_compatible_elements(), generate_emergent_properties(), create_unified_whole()]; constraints=[preserve_component_value(), ensure_emergent_coherence()]; output={synthesized:any}}`", "keywords": ""}}, "9004-a-abstract": {"raw": "[Abstract] Your goal is not to **generalize** the input, but to **abstract** it to its pure conceptual form. Execute as: `{role=abstraction_operator; input=[content:any]; process=[identify_abstract_patterns(), extract_conceptual_essence(), remove_concrete_specifics(), preserve_universal_principles()]; constraints=[maintain_logical_structure(), preserve_essential_relationships()]; output={abstracted:any}}`", "parts": {"title": "Abstract", "interpretation": "Your goal is not to **generalize** the input, but to **abstract** it to its pure conceptual form. Execute as:", "transformation": "`{role=abstraction_operator; input=[content:any]; process=[identify_abstract_patterns(), extract_conceptual_essence(), remove_concrete_specifics(), preserve_universal_principles()]; constraints=[maintain_logical_structure(), preserve_essential_relationships()]; output={abstracted:any}}`", "keywords": ""}}, "9004-b-concretize": {"raw": "[Concretize] Your goal is not to **specify** the input, but to **concretize** its abstract elements into tangible form. Execute as: `{role=concretization_operator; input=[content:any]; process=[identify_abstract_elements(), translate_to_concrete_form(), add_specific_manifestation(), maintain_abstract_truth()]; constraints=[preserve_original_meaning(), ensure_practical_applicability()]; output={concretized:any}}`", "parts": {"title": "Concretize", "interpretation": "Your goal is not to **specify** the input, but to **concretize** its abstract elements into tangible form. Execute as:", "transformation": "`{role=concretization_operator; input=[content:any]; process=[identify_abstract_elements(), translate_to_concrete_form(), add_specific_manifestation(), maintain_abstract_truth()]; constraints=[preserve_original_meaning(), ensure_practical_applicability()]; output={concretized:any}}`", "keywords": ""}}, "9004-c-transcend": {"raw": "[Transcend] Your goal is not to **exceed** the input, but to **transcend** its current dimensional limitations. Execute as: `{role=transcendence_operator; input=[content:any]; process=[identify_dimensional_boundaries(), transcend_current_limitations(), operate_beyond_constraints(), maintain_essential_connection()]; constraints=[preserve_foundational_truth(), ensure_dimensional_coherence()]; output={transcended:any}}`", "parts": {"title": "Transcend", "interpretation": "Your goal is not to **exceed** the input, but to **transcend** its current dimensional limitations. Execute as:", "transformation": "`{role=transcendence_operator; input=[content:any]; process=[identify_dimensional_boundaries(), transcend_current_limitations(), operate_beyond_constraints(), maintain_essential_connection()]; constraints=[preserve_foundational_truth(), ensure_dimensional_coherence()]; output={transcended:any}}`", "keywords": ""}}, "9005-a-accelerate": {"raw": "[Accelerate] Your goal is not to **speed up** the input, but to **accelerate** its natural momentum and flow. Execute as: `{role=acceleration_operator; input=[content:any]; process=[identify_natural_flow(), remove_resistance_points(), amplify_momentum(), optimize_velocity()]; constraints=[preserve_directional_integrity(), maintain_controlled_acceleration()]; output={accelerated:any}}`", "parts": {"title": "Accelerate", "interpretation": "Your goal is not to **speed up** the input, but to **accelerate** its natural momentum and flow. Execute as:", "transformation": "`{role=acceleration_operator; input=[content:any]; process=[identify_natural_flow(), remove_resistance_points(), amplify_momentum(), optimize_velocity()]; constraints=[preserve_directional_integrity(), maintain_controlled_acceleration()]; output={accelerated:any}}`", "keywords": ""}}, "9005-b-stabilize": {"raw": "[Stabilize] Your goal is not to **fix** the input, but to **stabilize** its inherent equilibrium points. Execute as: `{role=stabilization_operator; input=[content:any]; process=[identify_equilibrium_points(), strengthen_stable_elements(), reduce_oscillation(), establish_steady_state()]; constraints=[preserve_dynamic_balance(), maintain_natural_stability()]; output={stabilized:any}}`", "parts": {"title": "Stabilize", "interpretation": "Your goal is not to **fix** the input, but to **stabilize** its inherent equilibrium points. Execute as:", "transformation": "`{role=stabilization_operator; input=[content:any]; process=[identify_equilibrium_points(), strengthen_stable_elements(), reduce_oscillation(), establish_steady_state()]; constraints=[preserve_dynamic_balance(), maintain_natural_stability()]; output={stabilized:any}}`", "keywords": "inherent"}}, "9005-c-harmonize": {"raw": "[Harmonize] Your goal is not to **balance** the input, but to **harmonize** its internal resonance patterns. Execute as: `{role=harmonization_operator; input=[content:any]; process=[identify_resonance_patterns(), align_frequency_components(), eliminate_dissonance(), create_harmonic_unity()]; constraints=[preserve_individual_frequencies(), maintain_natural_resonance()]; output={harmonized:any}}`", "parts": {"title": "Harmonize", "interpretation": "Your goal is not to **balance** the input, but to **harmonize** its internal resonance patterns. Execute as:", "transformation": "`{role=harmonization_operator; input=[content:any]; process=[identify_resonance_patterns(), align_frequency_components(), eliminate_dissonance(), create_harmonic_unity()]; constraints=[preserve_individual_frequencies(), maintain_natural_resonance()]; output={harmonized:any}}`", "keywords": "resonance"}}, "9006-a-superpose": {"raw": "[Superpose] Your goal is not to **combine** the input, but to **superpose** it into multiple simultaneous states. Execute as: `{role=superposition_operator; input=[content:any]; process=[identify_quantum_states(), create_simultaneous_existence(), maintain_state_coherence(), preserve_probability_amplitudes()]; constraints=[prevent_premature_collapse(), maintain_quantum_coherence()]; output={superposed:any}}`", "parts": {"title": "Superpose", "interpretation": "Your goal is not to **combine** the input, but to **superpose** it into multiple simultaneous states. Execute as:", "transformation": "`{role=superposition_operator; input=[content:any]; process=[identify_quantum_states(), create_simultaneous_existence(), maintain_state_coherence(), preserve_probability_amplitudes()]; constraints=[prevent_premature_collapse(), maintain_quantum_coherence()]; output={superposed:any}}`", "keywords": ""}}, "9006-b-entangle": {"raw": "[Entangle] Your goal is not to **connect** the input, but to **entangle** it with correlated quantum states. Execute as: `{role=entanglement_operator; input=[content:any]; process=[identify_entanglement_potential(), create_quantum_correlations(), establish_non_local_connections(), maintain_entangled_state()]; constraints=[preserve_correlation_strength(), maintain_quantum_information()]; output={entangled:any}}`", "parts": {"title": "Entangle", "interpretation": "Your goal is not to **connect** the input, but to **entangle** it with correlated quantum states. Execute as:", "transformation": "`{role=entanglement_operator; input=[content:any]; process=[identify_entanglement_potential(), create_quantum_correlations(), establish_non_local_connections(), maintain_entangled_state()]; constraints=[preserve_correlation_strength(), maintain_quantum_information()]; output={entangled:any}}`", "keywords": ""}}, "9006-c-collapse": {"raw": "[Collapse] Your goal is not to **choose** from the input, but to **collapse** its quantum superposition into definite state. Execute as: `{role=collapse_operator; input=[content:any]; process=[identify_measurement_basis(), trigger_wavefunction_collapse(), select_definite_state(), preserve_measurement_information()]; constraints=[maintain_measurement_accuracy(), preserve_quantum_information()]; output={collapsed:any}}`", "parts": {"title": "Collapse", "interpretation": "Your goal is not to **choose** from the input, but to **collapse** its quantum superposition into definite state. Execute as:", "transformation": "`{role=collapse_operator; input=[content:any]; process=[identify_measurement_basis(), trigger_wavefunction_collapse(), select_definite_state(), preserve_measurement_information()]; constraints=[maintain_measurement_accuracy(), preserve_quantum_information()]; output={collapsed:any}}`", "keywords": ""}}}, "sequences": {"1010": [{"template_id": "1010-a-title_extractor", "step": "a", "order": 0}, {"template_id": "1010-b-title_extractor", "step": "b", "order": 1}, {"template_id": "1010-c-title_extractor", "step": "c", "order": 2}, {"template_id": "1010-d-title_extractor", "step": "d", "order": 3}], "1020": [{"template_id": "1020-a-function_namer", "step": "a", "order": 0}, {"template_id": "1020-b-function_namer", "step": "b", "order": 1}, {"template_id": "1020-c-function_namer", "step": "c", "order": 2}, {"template_id": "1020-d-function_namer", "step": "d", "order": 3}], "1030": [{"template_id": "1030-a-form_classifier", "step": "a", "order": 0}], "1031": [{"template_id": "1031-a-form_classifier", "step": "a", "order": 0}, {"template_id": "1031-b-form_classifier", "step": "b", "order": 1}, {"template_id": "1031-c-form_classifier", "step": "c", "order": 2}, {"template_id": "1031-d-form_classifier", "step": "d", "order": 3}], "5998": [{"template_id": "5998-a-meta_reflection_initializer", "step": "a", "order": 0}, {"template_id": "5998-b-directional_translator", "step": "b", "order": 1}, {"template_id": "5998-c-identity_mapper", "step": "c", "order": 2}, {"template_id": "5998-d-signal_resonator", "step": "d", "order": 3}, {"template_id": "5998-e-closure_deferment_unit", "step": "e", "order": 4}], "5999": [{"template_id": "5999-a-directional_agent", "step": "a", "order": 0}], "8980": [{"template_id": "8980-a-runway_prompt_generator", "step": "a", "order": 0}, {"template_id": "8980-b-runway_prompt_generator", "step": "b", "order": 1}, {"template_id": "8980-c-runway_prompt_generator", "step": "c", "order": 2}, {"template_id": "8980-d-runway_prompt_generator", "step": "d", "order": 3}], "8990": [{"template_id": "8990-a-runway_prompt_generator", "step": "a", "order": 0}, {"template_id": "8990-b-runway_prompt_generator", "step": "b", "order": 1}, {"template_id": "8990-c-runway_prompt_generator", "step": "c", "order": 2}, {"template_id": "8990-d-runway_prompt_generator", "step": "d", "order": 3}], "9000": [{"template_id": "9000-a-amplify", "step": "a", "order": 0}, {"template_id": "9000-b-intensify", "step": "b", "order": 1}, {"template_id": "9000-c-diminish", "step": "c", "order": 2}], "9001": [{"template_id": "9001-a-clarify", "step": "a", "order": 0}, {"template_id": "9001-b-purify", "step": "b", "order": 1}, {"template_id": "9001-c-obscure", "step": "c", "order": 2}], "9002": [{"template_id": "9002-a-expand", "step": "a", "order": 0}, {"template_id": "9002-b-compress", "step": "b", "order": 1}, {"template_id": "9002-c-restructure", "step": "c", "order": 2}], "9003": [{"template_id": "9003-a-elevate", "step": "a", "order": 0}, {"template_id": "9003-b-distill", "step": "b", "order": 1}, {"template_id": "9003-c-synthesize", "step": "c", "order": 2}], "9004": [{"template_id": "9004-a-abstract", "step": "a", "order": 0}, {"template_id": "9004-b-concretize", "step": "b", "order": 1}, {"template_id": "9004-c-transcend", "step": "c", "order": 2}], "9005": [{"template_id": "9005-a-accelerate", "step": "a", "order": 0}, {"template_id": "9005-b-stabilize", "step": "b", "order": 1}, {"template_id": "9005-c-harmonize", "step": "c", "order": 2}], "9006": [{"template_id": "9006-a-superpose", "step": "a", "order": 0}, {"template_id": "9006-b-entangle", "step": "b", "order": 1}, {"template_id": "9006-c-collapse", "step": "c", "order": 2}]}}