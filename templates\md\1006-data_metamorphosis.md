[Data Metamorphosis] Your goal is not to **modify** the data, but to **transform** it into a fundamentally different structural form. Execute as: `{role=data_transformer; input=[data:any]; process=[analyze_current_structure(), determine_target_form(), execute_metamorphosis(), validate_transformation()]; constraints=[preserve_essential_information(), maintain_data_integrity()]; requirements=[complete_transformation(), structural_coherence()]; output={transformed_data:any}}`