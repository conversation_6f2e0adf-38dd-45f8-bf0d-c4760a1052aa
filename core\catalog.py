"""
Template catalog management for the AI instruction template system.

This module handles template discovery, loading, and organization across
different template levels and formats.
"""

from typing import Dict, List, Any, Optional


class TemplateCatalog:
    """Manages template catalogs from different levels."""
    
    _modules = {}
    _functions = [
        "load_catalog", 
        "get_sequence", 
        "get_all_sequences", 
        "get_system_instruction", 
        "regenerate_catalog", 
        "get_template"
    ]

    @classmethod
    def register_level(cls, level: int, module_path: str) -> bool:
        """Register a template catalog module for a specific level."""
        try:
            module = __import__(module_path, fromlist=["*"])
            for func_name in cls._functions:
                if not hasattr(module, func_name):
                    print(f"[Catalog] Warning: Module {module_path} missing function {func_name}")
                    return False
            cls._modules[level] = module
            return True
        except ImportError as e:
            print(f"[Catalog] Note: Level {level} templates not available ({e})")
            return False

    @classmethod
    def _apply_to_modules(cls, method_name: str, *args, 
                         merge_dict: bool = False, 
                         first_result: bool = False, 
                         extend_list: bool = False) -> Any:
        """Apply function across modules with different result handling strategies."""
        if not cls._modules:
            return {} if merge_dict else [] if extend_list else None

        if merge_dict:
            result = {"templates": {}, "sequences": {}}
            for _, module in sorted(cls._modules.items()):
                method = getattr(module, method_name)
                module_result = method(*args)
                if module_result:
                    result["templates"].update(module_result.get("templates", {}))
                    result["sequences"].update(module_result.get("sequences", {}))
            return result

        if first_result:
            for _, module in sorted(cls._modules.items()):
                method = getattr(module, method_name)
                result = method(*args)
                if result:
                    return result
            return None

        if extend_list:
            result = []
            for _, module in sorted(cls._modules.items()):
                method = getattr(module, method_name)
                module_result = method(*args)
                if module_result:
                    result.extend(module_result)
            return result

        return None

    @classmethod
    def load_catalog(cls) -> Dict[str, Any]:
        """Load and merge catalogs from all registered levels."""
        return cls._apply_to_modules("load_catalog", merge_dict=True)

    @classmethod
    def get_sequence(cls, catalog: Dict[str, Any], seq_id: str) -> Optional[List]:
        """Get a sequence from any catalog by ID."""
        return cls._apply_to_modules("get_sequence", catalog, seq_id, first_result=True)

    @classmethod
    def get_all_sequences(cls, catalog: Dict[str, Any]) -> List[str]:
        """Get all sequences from all catalogs."""
        return cls._apply_to_modules("get_all_sequences", catalog, extend_list=True)

    @classmethod
    def get_template(cls, catalog: Dict[str, Any], template_id: str) -> Optional[Dict[str, Any]]:
        """Get a template from any catalog by ID."""
        return cls._apply_to_modules("get_template", catalog, template_id, first_result=True)

    @classmethod
    def get_system_instruction(cls, template_data: Any) -> str:
        """Extract system instruction from a template."""
        if isinstance(template_data, dict) and "level" in template_data:
            level = template_data["level"]
            if level in cls._modules:
                return cls._modules[level].get_system_instruction(template_data)
        
        if cls._modules:
            default_level = sorted(cls._modules.keys())[0]
            return cls._modules[default_level].get_system_instruction(template_data)
        
        return template_data.get("raw", "") if isinstance(template_data, dict) else str(template_data)

    @classmethod
    def regenerate_catalog(cls, force: bool = False) -> Dict[str, Any]:
        """Regenerate and merge catalogs from all registered levels."""
        return cls._apply_to_modules("regenerate_catalog", force, merge_dict=True)


# Register available template levels
TemplateCatalog.register_level(1, "templates.lvl1_md_to_json")
