# Execution Guide: How to Use the System

## Overview

This guide covers all methods of using the AI Systems template framework, from command-line interface to programmatic integration.

## Command Line Interface

### Basic Usage

**Execute a Single Template Sequence**
```bash
# Execute sequence 1031 with default model
python -m core --sequence 1031 --prompt "Analyze this code structure"

# Execute with specific model
python -m core --sequence 1031 --models gpt-4o --prompt "Analyze this code structure"

# Execute with multiple models
python -m core --sequence 1031 --models gpt-4o claude-3-sonnet --prompt "Analyze this code structure"
```

**Execute Multi-Step Sequences**
```bash
# Execute steps a through c of sequence 9000
python -m core --sequence 9000:a-c --prompt "Transform this content"

# Execute multiple sequences
python -m core --sequence "1031|9000" --prompt "Process this input"

# Complex sequence specification
python -m core --sequence "1031:a-c|9000:a-b" --prompt "Multi-stage processing"
```

### Sequence Specification Formats

**Single Sequence**
```bash
--sequence 1031              # Entire sequence
--sequence 9000              # All steps in sequence 9000
```

**Specific Steps**
```bash
--sequence 1031:a            # Only step 'a' of sequence 1031
--sequence 9000:a-c          # Steps 'a' through 'c' of sequence 9000
```

**Multiple Sequences**
```bash
--sequence "1031|9000"       # Both sequences in parallel
--sequence "1031:a-c|9000:a-b"  # Specific steps from multiple sequences
```

**Embedded in Prompts**
```bash
# Using [SEQ:...] syntax
python -m core --prompt "[SEQ:1031:a-c] Analyze this code structure"

# Using --seq= syntax
python -m core --prompt "Analyze this code structure --seq=1031:a-c"
```

### Output Options

**File Output**
```bash
# Save to specific file
python -m core --sequence 1031 --prompt "Test" --output results.json

# Custom output directory
python -m core --sequence 1031 --prompt "Test" --output-dir ./results/

# Minified output
python -m core --sequence 1031 --prompt "Test" --minified
```

**Display Control**
```bash
# Hide system instructions
python -m core --sequence 1031 --prompt "Test" --no-show-instructions

# Hide responses (useful for testing)
python -m core --sequence 1031 --prompt "Test" --no-show-responses

# Show only final results
python -m core --sequence 1031 --prompt "Test" --no-show-inputs --no-show-instructions
```

### Execution Modes

**Chain Mode**
```bash
# Enable chain mode (output of step N becomes input of step N+1)
python -m core --sequence 9000:a-c --prompt "Initial input" --chain

# Disable chain mode (all steps use original prompt)
python -m core --sequence 9000:a-c --prompt "Initial input" --no-chain
```

**Model Configuration**
```bash
# Use specific provider's default model
python -m core --sequence 1031 --provider anthropic --prompt "Test"

# Override model parameters
python -m core --sequence 1031 --models gpt-4o --temperature 0.7 --max-tokens 1000 --prompt "Test"
```

### Template Management

**Catalog Operations**
```bash
# Regenerate template catalog
python -m core --regenerate-catalog

# Force regeneration even if up to date
python -m core --regenerate-catalog --force

# List available sequences
python -m core --list-sequences

# Show template details
python -m core --show-template 1031-a-form_classifier

# Show catalog information
python -m core --show-catalog-info
```

**Validation**
```bash
# Validate all templates
python -m core --validate-templates

# Validate specific template
python -m core --validate-template 1031-a-form_classifier

# Check template compliance
python -m core --check-compliance
```

## Programmatic Usage

### Basic Python Integration

```python
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core import Config, TemplateCatalog, SequenceManager
from core.models import ExecutorConfig

# Load template catalog
catalog = TemplateCatalog.load_catalog()

# Get a specific template
template = TemplateCatalog.get_template(catalog, "1031-a-form_classifier")

# Get a sequence
sequence_steps = TemplateCatalog.get_sequence(catalog, "1031")

# Parse sequence specifications
parsed = SequenceManager.parse_sequence_spec("1031:a-c|9000:a-b")
```

### Advanced Integration

```python
from core.executor import SequenceExecutor
from core.config import Config

# Configure the system
Config.configure_litellm()

# Create executor configuration
config = ExecutorConfig(
    sequence_steps=sequence_steps,
    user_prompt="Your input here",
    sequence_id="1031",
    models=["gpt-4o", "claude-3-sonnet"],
    chain_mode=True,
    show_responses=True
)

# Execute sequence
executor = SequenceExecutor()
results = await executor.execute_sequence(config)
```

### Custom Processing

```python
from core.utils import ValidationUtils, PromptParser

# Validate template structure
template_content = "[Test] Your goal is not to **analyze**, but to **transform**. Execute as: `{role=test; input=[content:any]; output={result:any}}`"
validation = ValidationUtils.validate_template_structure(template_content)

# Parse embedded sequence specifications
prompt = "[SEQ:1031:a-c] Analyze this content"
cleaned_prompt, sequence_spec = PromptParser.extract_sequence_from_prompt(prompt)

# Extract transformation components
transformation = "`{role=test; input=[content:any]; process=[step1(), step2()]; output={result:any}}`"
components = ValidationUtils.extract_transformation_components(transformation)
```

## Environment Configuration

### API Keys Setup

Create a `.env` file in the project root:
```bash
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here
DEEPSEEK_API_KEY=your_deepseek_key_here
```

### Environment Variables

```bash
# Set default model
export AI_SYSTEMS_DEFAULT_MODEL=gpt-4o

# Set default output directory
export AI_SYSTEMS_OUTPUT_DIR=./results/

# Set default provider
export AI_SYSTEMS_DEFAULT_PROVIDER=openai
```

## Common Usage Patterns

### Content Analysis Pipeline

```bash
# Multi-step content analysis
python -m core --sequence "1031:a-d|9001:a-b" --prompt "$(cat document.txt)" --chain
```

### Code Processing

```bash
# Code analysis and optimization
python -m core --sequence "1020:a-d" --prompt "$(cat script.py)" --models gpt-4o
```

### Prompt Engineering

```bash
# Test prompt variations across models
python -m core --sequence "9000:a-c" --models gpt-4o claude-3-sonnet gemini-2.5-pro --prompt "Original prompt"
```

### Directional Transformations

```bash
# Apply directional vectors
python -m core --sequence "9000:a" --prompt "Content to amplify"
python -m core --sequence "9001:a" --prompt "Content to clarify"
python -m core --sequence "9003:b" --prompt "Content to distill"
```

### Batch Processing

```bash
# Process multiple inputs
for file in *.txt; do
    python -m core --sequence 1031 --prompt "$(cat $file)" --output "results_$(basename $file .txt).json"
done
```

## Output Formats

### Standard JSON Output

```json
{
  "user_prompt": "Your input prompt",
  "sequence_id": "1031",
  "results": [
    {
      "instruction": "System instruction text",
      "step": "a",
      "title": "Form Classifier",
      "responses": {
        "gpt-4o": {
          "model": "gpt-4o",
          "content": "Response content"
        }
      }
    }
  ],
  "total_cost": 0.0025,
  "execution_time": 2.34
}
```

### Minified Output

```json
{"user_prompt":"Your input","sequence_id":"1031","results":[{"step":"a","responses":{"gpt-4o":{"content":"Response"}}}]}
```

### Streaming Output

Real-time display during execution:
```
[1031-a] Form Classifier
Input: Your input prompt
Instruction: [System instruction]

gpt-4o: [Response content]
claude-3-sonnet: [Response content]

Cost: $0.0025 | Time: 2.34s
```

## Error Handling

### Common Issues

**Template Not Found**
```bash
# List available sequences
python -m core --list-sequences

# Check specific template
python -m core --show-template template-id
```

**Model Access Issues**
```bash
# Check API keys
python -m core --check-api-keys

# Test model availability
python -m core --test-models
```

**Sequence Specification Errors**
```bash
# Validate sequence specification
python -m core --validate-sequence "1031:a-c|9000:a-b"
```

### Debug Mode

```bash
# Enable verbose output
python -m core --sequence 1031 --prompt "Test" --verbose

# Show detailed execution info
python -m core --sequence 1031 --prompt "Test" --debug
```

## Performance Optimization

### Concurrent Execution

```bash
# Limit concurrent model calls
python -m core --sequence 1031 --models gpt-4o claude-3-sonnet --max-concurrent 2
```

### Cost Management

```bash
# Set cost limits
python -m core --sequence 1031 --prompt "Test" --max-cost 0.10

# Use cheaper models for testing
python -m core --sequence 1031 --prompt "Test" --models gpt-3.5-turbo
```

### Output Optimization

```bash
# Use minified output for large results
python -m core --sequence 1031 --prompt "Test" --minified

# Disable streaming for batch processing
python -m core --sequence 1031 --prompt "Test" --no-stream
```

This guide provides comprehensive coverage of all execution methods and options available in the AI Systems framework.
