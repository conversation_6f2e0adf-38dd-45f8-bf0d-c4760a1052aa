# Quick Reference: AI Systems Template Framework

## Template Structure (Mandatory)

```
[Title] Interpretation Execute as: `{Transformation}`
```

### Three Parts
1. **[Title]** - Purpose in square brackets
2. **Interpretation** - Goal negation + instructions + "Execute as:"
3. **`{Transformation}`** - Machine-parsable parameters in backticks

## Goal Negation Pattern (Required)

```
Your goal is not to **[wrong_action]**, but to **[correct_transformation]**
```

## Transformation Block Structure

```
{
  role=specific_role_name;
  input=[parameter:type];
  process=[step1(), step2(), step3()];
  constraints=[limit1(), limit2()];
  requirements=[req1(), req2()];
  output={result:type}
}
```

## File Naming Convention

```
<sequence_id>-<step>-<descriptive-name>.md
```

Examples:
- `1031-a-form-classifier.md`
- `9000-a-amplify.md`
- `0001-instruction-converter.md`

## Directional Vectors

### Intensity
- **amplify**: Intensify inherent qualities
- **intensify**: Compress to maximum density
- **diminish**: Reduce intensity, preserve form

### Clarity
- **clarify**: Enhance transparency
- **purify**: Remove non-essential elements
- **obscure**: Add complexity layers

### Structure
- **expand**: Extend natural boundaries
- **compress**: Maximize density without loss
- **restructure**: Transform organization pattern

### Transformation
- **elevate**: Transform to higher level
- **distill**: Extract absolute essence
- **synthesize**: Create unified emergent form

### Meta
- **abstract**: Extract to conceptual form
- **concretize**: Translate to tangible form
- **transcend**: Operate beyond limitations

## CLI Commands

### Basic Execution
```bash
python -m core --sequence 1031 --prompt "Your input"
python -m core --sequence 9000:a-c --prompt "Transform this"
python -m core --sequence "1031|9000" --prompt "Multi-sequence"
```

### Model Selection
```bash
--models gpt-4o
--models gpt-4o claude-3-sonnet
--provider anthropic
```

### Output Options
```bash
--output results.json
--minified
--no-show-instructions
--chain / --no-chain
```

### Template Management
```bash
--regenerate-catalog
--list-sequences
--show-template template-id
--validate-templates
```

## Sequence Specifications

```
1031              # Entire sequence
1031:a            # Single step
1031:a-c          # Range of steps
1031|9000         # Multiple sequences
1031:a-c|9000:a-b # Complex specification
```

## Embedded Sequence Specs

```bash
# In prompts
"[SEQ:1031:a-c] Your prompt here"
"Your prompt --seq=1031:a-c"
```

## Data Types

- `str` - String
- `int` - Integer
- `float` - Float
- `bool` - Boolean
- `any` - Any type
- `list` - List/Array
- `dict` - Dictionary/Object

## Forbidden Language

### In Interpretation
- First-person: *I, me, my, we, us*
- Conversational: *please, thank you, let's*
- Uncertain: *maybe, perhaps, might, could*
- Questions in directives

### In Role
- Generic: *assistant, helper, AI*
- Vague: *processor, handler*

## Template Examples

### Simple Template
```
[Text Summarizer] Your goal is not to **repeat** the input text, but to **distill** it into essential points. Execute as: `{role=content_summarizer; input=[text:str]; process=[identify_main_points(), extract_key_details(), synthesize_summary()]; output={summary:str}}`
```

### Directional Vector
```
[Amplify] Your goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification. Execute as: `{role=amplification_operator; input=[content:any]; process=[identify_core_qualities(), intensify_primary_characteristics(), magnify_essential_elements()]; output={amplified:any}}`
```

### Multi-Parameter
```
[Code Optimizer] Your goal is not to **rewrite** the code arbitrarily, but to **optimize** it for performance. Execute as: `{role=code_optimizer; input=[source_code:str, language:str]; process=[analyze_structure(), identify_inefficiencies(), apply_optimizations()]; output={optimized_code:str, report:dict}}`
```

## Validation Checklist

- [ ] Three-part structure
- [ ] Goal negation pattern
- [ ] Specific role (not generic)
- [ ] Typed input parameters
- [ ] Actionable process steps
- [ ] Typed output format
- [ ] No forbidden language
- [ ] Proper file naming

## Common Patterns

### Content Processing
```bash
python -m core --sequence "1031:a-d|9001:a-b" --prompt "$(cat file.txt)" --chain
```

### Code Analysis
```bash
python -m core --sequence "1020:a-d" --prompt "$(cat script.py)"
```

### Directional Transformation
```bash
python -m core --sequence "9000:a" --prompt "Content to amplify"
```

### Multi-Model Testing
```bash
python -m core --sequence 1031 --models gpt-4o claude-3-sonnet gemini-2.5-pro
```

## Environment Setup

### API Keys (.env file)
```
OPENAI_API_KEY=your_key
ANTHROPIC_API_KEY=your_key
GOOGLE_API_KEY=your_key
DEEPSEEK_API_KEY=your_key
```

### Environment Variables
```bash
export AI_SYSTEMS_DEFAULT_MODEL=gpt-4o
export AI_SYSTEMS_OUTPUT_DIR=./results/
```

## Model Registry

### OpenAI
- `gpt-4o`, `gpt-4.1`, `gpt-3.5-turbo`, `o3-mini`

### Anthropic
- `claude-3-opus`, `claude-3-sonnet`, `claude-3-haiku`
- `claude-3.7-sonnet`, `claude-sonnet-4-20250514`

### Google
- `gemini-pro`, `gemini-flash`, `gemini-2-flash`, `gemini-2.5-pro`

### Deepseek
- `deepseek-reasoner`, `deepseek-coder`, `deepseek-chat`

## Vector Algebra

### Commutative
```
amplify + clarify = clarify + amplify
```

### Non-Commutative
```
distill → amplify ≠ amplify → distill
```

### Inverse Operations
```
amplify ↔ diminish
expand ↔ compress
clarify ↔ obscure
abstract ↔ concretize
```

## Error Handling

### Template Issues
```bash
--list-sequences              # Show available
--show-template template-id   # Check specific
--validate-templates          # Check all
```

### Model Issues
```bash
--check-api-keys             # Verify keys
--test-models                # Test availability
```

### Debug
```bash
--verbose                    # Detailed output
--debug                      # Debug info
```

## Output Formats

### Standard JSON
```json
{
  "user_prompt": "Input",
  "sequence_id": "1031",
  "results": [...],
  "total_cost": 0.0025
}
```

### Minified
```json
{"user_prompt":"Input","results":[...]}
```

## Performance Tips

- Use `--minified` for large outputs
- Use `--max-concurrent 2` to limit API calls
- Use cheaper models for testing: `gpt-3.5-turbo`
- Use `--no-stream` for batch processing

## Key Principles

1. **Vector Independence**: Transformations work on any content
2. **Essence Preservation**: Core identity maintained
3. **Composability**: Vectors can be chained
4. **Type Safety**: All parameters typed
5. **Context-Free**: No domain knowledge required
6. **Consistency**: Predictable results
