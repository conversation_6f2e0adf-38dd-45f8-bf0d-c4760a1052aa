"""
AI Systems Template Framework

Stage-aware template generation with lifecycle management.
"""

from .catalog import (
    TemplateConfigMD, generate_catalog, load_catalog, get_template,
    get_sequence, get_all_sequences, regenerate_catalog,
    LIFECYCLE_STAGES, SEMANTIC_CATEGORIES
)

from .stage1_prototypes import (
    create_amplifier, create_builder, create_clarifier,
    create_formatter, create_identifier, create_optimizer,
    create_reducer, create_transformer, create_translator,
    create_sequence, auto_generate_id, PrototypeGenerator
)

from .stage2_candidates import (
    promote_to_stage2, suggest_placement, list_stage1_templates,
    list_stage2_templates, CandidateManager
)

from .stage3_production import (
    promote_to_stage3, manual_placement, show_production_architecture,
    suggest_production_ranges, list_stage3_templates, ProductionManager
)

__version__ = "2.0.0"
__stage_ranges__ = {
    "stage1": (1000, 1999),
    "stage2": (2000, 2999),
    "stage3": (3000, 3999)
}

# Simplified API for common operations
def create_prototype(category: str, title: str, target: str = None) -> str:
    """Create stage1 prototype with automatic ID assignment."""
    generators = {
        "amplifier": create_amplifier,
        "builder": create_builder,
        "clarifier": create_clarifier,
        "formatter": create_formatter,
        "identifier": create_identifier,
        "optimizer": create_optimizer,
        "reducer": create_reducer,
        "transformer": create_transformer,
        "translator": create_translator
    }

    if category not in generators:
        raise ValueError(f"Unknown category: {category}. Available: {list(generators.keys())}")

    return generators[category](title, target)

def promote_template(template_id: str, target_stage: str, target_id: int = None) -> str:
    """Promote template between stages."""
    if target_stage == "stage2":
        return promote_to_stage2(template_id)
    elif target_stage == "stage3":
        return promote_to_stage3(template_id, target_id)
    else:
        raise ValueError(f"Invalid target stage: {target_stage}. Use 'stage2' or 'stage3'")

def list_templates_by_stage(stage: str) -> list:
    """List all templates in a specific stage."""
    if stage == "stage1":
        return list_stage1_templates()
    elif stage == "stage2":
        return list_stage2_templates()
    elif stage == "stage3":
        return list_stage3_templates()
    else:
        raise ValueError(f"Invalid stage: {stage}. Use 'stage1', 'stage2', or 'stage3'")

def get_stage_info(stage: str = None) -> dict:
    """Get information about lifecycle stages."""
    if stage:
        if stage in __stage_ranges__:
            start, end = __stage_ranges__[stage]
            config = LIFECYCLE_STAGES[stage]
            return {
                "stage": stage,
                "range": (start, end),
                "auto_id": config.auto_id,
                "description": config.description
            }
        else:
            raise ValueError(f"Invalid stage: {stage}")
    else:
        return {stage: get_stage_info(stage) for stage in __stage_ranges__}

# Export all main components
__all__ = [
    # Core catalog functions
    "TemplateConfigMD", "generate_catalog", "load_catalog", "get_template",
    "get_sequence", "get_all_sequences", "regenerate_catalog",

    # Stage 1 prototyping
    "create_amplifier", "create_builder", "create_clarifier", "create_formatter",
    "create_identifier", "create_optimizer", "create_reducer", "create_transformer",
    "create_translator", "create_sequence", "auto_generate_id", "PrototypeGenerator",

    # Stage 2 candidates
    "promote_to_stage2", "suggest_placement", "list_stage1_templates",
    "list_stage2_templates", "CandidateManager",

    # Stage 3 production
    "promote_to_stage3", "manual_placement", "show_production_architecture",
    "suggest_production_ranges", "list_stage3_templates", "ProductionManager",

    # Simplified API
    "create_prototype", "promote_template", "list_templates_by_stage", "get_stage_info",

    # Configuration
    "LIFECYCLE_STAGES", "SEMANTIC_CATEGORIES", "__stage_ranges__"
]
