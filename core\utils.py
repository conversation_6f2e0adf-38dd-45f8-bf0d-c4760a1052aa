"""
Utility functions for the AI instruction template system.

This module contains helper functions for prompt parsing, sequence management,
and other common operations.
"""

import re
from typing import <PERSON>ple, Optional, List, Dict, Any


class PromptParser:
    """Utilities for parsing prompts with embedded sequence specifications."""

    @staticmethod
    def extract_sequence_from_prompt(prompt: str) -> Tuple[str, Optional[str]]:
        """
        Extract sequence specification from prompt if present.

        Supports multiple formats:
        - [SEQ:0194:c|0221] Your prompt here
        - Your prompt here [SEQ:0194:c|0221]
        - Your prompt here --seq=0194:c|0221

        Returns:
            tuple: (cleaned_prompt, sequence_spec) or (original_prompt, None)
        """
        if not prompt:
            return prompt, None

        # Pattern 1: [SEQ:sequence_spec] at start or end
        seq_pattern1 = r'\[SEQ:([^\]]+)\]'
        match1 = re.search(seq_pattern1, prompt)

        if match1:
            sequence_spec = match1.group(1)
            cleaned_prompt = re.sub(seq_pattern1, '', prompt).strip()
            return cleaned_prompt, sequence_spec

        # Pattern 2: --seq=sequence_spec anywhere in prompt
        seq_pattern2 = r'--seq=([^\s]+)'
        match2 = re.search(seq_pattern2, prompt)

        if match2:
            sequence_spec = match2.group(1)
            cleaned_prompt = re.sub(seq_pattern2, '', prompt).strip()
            return cleaned_prompt, sequence_spec

        return prompt, None


class SequenceManager:
    """Utilities for managing and parsing sequence specifications."""

    @staticmethod
    def parse_sequence_spec(sequence_spec: str) -> List[str]:
        """
        Parse sequence specification into individual sequence IDs.
        
        Supports formats like:
        - "1031" -> ["1031"]
        - "1031:a-c" -> ["1031:a", "1031:b", "1031:c"]
        - "1031|9000" -> ["1031", "9000"]
        - "1031:a-c|9000:a-b" -> ["1031:a", "1031:b", "1031:c", "9000:a", "9000:b"]
        
        Args:
            sequence_spec: Sequence specification string
            
        Returns:
            List of individual sequence identifiers
        """
        if not sequence_spec:
            return []

        sequences = []
        
        # Split by pipe for multiple sequences
        parts = sequence_spec.split('|')
        
        for part in parts:
            part = part.strip()
            if ':' in part and '-' in part:
                # Handle range specification like "1031:a-c"
                seq_id, range_spec = part.split(':', 1)
                if '-' in range_spec:
                    start, end = range_spec.split('-', 1)
                    start_ord = ord(start.lower())
                    end_ord = ord(end.lower())
                    for i in range(start_ord, end_ord + 1):
                        sequences.append(f"{seq_id}:{chr(i)}")
                else:
                    sequences.append(part)
            else:
                sequences.append(part)
        
        return sequences

    @staticmethod
    def resolve_sequence_steps(catalog: Dict[str, Any], sequence_id: str) -> List[Tuple[str, Any]]:
        """
        Resolve a sequence ID to actual template steps.
        
        Args:
            catalog: Template catalog
            sequence_id: Sequence identifier (e.g., "1031", "1031:a", "1031:a-c")
            
        Returns:
            List of (step_id, template_data) tuples
        """
        from .catalog import TemplateCatalog
        
        if ':' in sequence_id:
            # Specific step or range
            base_id, step_spec = sequence_id.split(':', 1)
            sequence_steps = TemplateCatalog.get_sequence(catalog, base_id)
            
            if not sequence_steps:
                return []
            
            if '-' in step_spec:
                # Range of steps
                start, end = step_spec.split('-', 1)
                start_ord = ord(start.lower())
                end_ord = ord(end.lower())
                
                filtered_steps = []
                for step_id, template_data in sequence_steps:
                    if start_ord <= ord(step_id.lower()) <= end_ord:
                        filtered_steps.append((step_id, template_data))
                return filtered_steps
            else:
                # Single step
                for step_id, template_data in sequence_steps:
                    if step_id.lower() == step_spec.lower():
                        return [(step_id, template_data)]
                return []
        else:
            # Entire sequence
            return TemplateCatalog.get_sequence(catalog, sequence_id) or []


class ValidationUtils:
    """Utilities for validating templates and configurations."""

    @staticmethod
    def validate_template_structure(template_content: str) -> Dict[str, Any]:
        """
        Validate that a template follows the required three-part structure.
        
        Args:
            template_content: Raw template content
            
        Returns:
            Dictionary with validation results and extracted components
        """
        # Pattern for three-part structure: [Title] Interpretation `{Transformation}`
        pattern = re.compile(
            r"\[(.*?)\]"     # Group 1: Title
            r"\s*"           # Whitespace
            r"(.*?)"         # Group 2: Interpretation
            r"\s*"           # Whitespace
            r"(`\{.*?\}`)"   # Group 3: Transformation
        )
        
        match = pattern.search(template_content.strip())
        
        if not match:
            return {
                "valid": False,
                "error": "Template does not match required three-part structure",
                "title": "",
                "interpretation": "",
                "transformation": ""
            }
        
        title = match.group(1).strip()
        interpretation = match.group(2).strip()
        transformation = match.group(3).strip()
        
        # Validate goal negation pattern
        goal_negation_pattern = r"Your goal is not to \*\*(.*?)\*\*, but to \*\*(.*?)\*\*"
        has_goal_negation = bool(re.search(goal_negation_pattern, interpretation))
        
        return {
            "valid": True,
            "title": title,
            "interpretation": interpretation,
            "transformation": transformation,
            "has_goal_negation": has_goal_negation,
            "warnings": [] if has_goal_negation else ["Missing goal negation pattern"]
        }

    @staticmethod
    def extract_transformation_components(transformation: str) -> Dict[str, Any]:
        """
        Extract components from transformation block.
        
        Args:
            transformation: Transformation string like `{role=...; input=[...]; ...}`
            
        Returns:
            Dictionary with extracted components
        """
        # Remove backticks and outer braces
        content = transformation.strip('`{}')
        
        components = {}
        
        # Simple parsing for key=value pairs
        # This is a basic implementation - could be enhanced for complex nested structures
        parts = content.split(';')
        
        for part in parts:
            part = part.strip()
            if '=' in part:
                key, value = part.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                # Handle array-like values [...]
                if value.startswith('[') and value.endswith(']'):
                    # Simple array parsing
                    array_content = value[1:-1].strip()
                    if array_content:
                        components[key] = [item.strip() for item in array_content.split(',')]
                    else:
                        components[key] = []
                # Handle object-like values {...}
                elif value.startswith('{') and value.endswith('}'):
                    # Simple object parsing
                    obj_content = value[1:-1].strip()
                    obj_components = {}
                    if obj_content:
                        for obj_part in obj_content.split(','):
                            if ':' in obj_part:
                                obj_key, obj_value = obj_part.split(':', 1)
                                obj_components[obj_key.strip()] = obj_value.strip()
                    components[key] = obj_components
                else:
                    components[key] = value
        
        return components
