"""
AI Systems: Template-Based Instruction Processing

Core package for the AI instruction template system.
"""

__version__ = "0.1.0"
__author__ = "AI Systems Team"

from .config import Config, PathUtils
from .models import (
    ExecutorConfig,
    ModelResponse,
    InstructionResult,
    ExecutionResults,
    TemplateMetadata,
    SequenceStep,
    CatalogMetadata,
    TemplateCatalog as TemplateCatalogModel
)
from .catalog import TemplateCatalog
from .utils import Prompt<PERSON>ars<PERSON>, SequenceManager, ValidationUtils
from .validation import TemplateValidator, ValidationResult, validate_template_file, print_validation_report

__all__ = [
    "Config",
    "PathUtils",
    "ExecutorConfig",
    "ModelResponse",
    "InstructionResult",
    "ExecutionResults",
    "TemplateMetadata",
    "SequenceStep",
    "CatalogMetadata",
    "TemplateCatalogModel",
    "TemplateCatalog",
    "PromptParser",
    "SequenceManager",
    "ValidationUtils",
    "TemplateValidator",
    "ValidationResult",
    "validate_template_file",
    "print_validation_report"
]
