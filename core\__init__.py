"""
AI Systems: Template-Based Instruction Processing

Core package for the AI instruction template system.
"""

__version__ = "0.1.0"
__author__ = "AI Systems Team"

from .config import Config, PathUtils
from .models import (
    ExecutorConfig,
    ModelResponse,
    InstructionResult,
    ExecutionResults,
    TemplateMetadata,
    SequenceStep,
    CatalogMetadata,
    TemplateCatalog as TemplateCatalogModel
)
from .catalog import TemplateCatalog
from .utils import PromptParser, SequenceManager, ValidationUtils

__all__ = [
    "Config",
    "PathUtils",
    "ExecutorConfig",
    "ModelResponse",
    "InstructionResult",
    "ExecutionResults",
    "TemplateMetadata",
    "SequenceStep",
    "CatalogMetadata",
    "TemplateCatalogModel",
    "TemplateCatalog",
    "PromptParser",
    "SequenceManager",
    "ValidationUtils"
]
