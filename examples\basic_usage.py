"""
Basic usage examples for the AI Systems template framework.

This file demonstrates how to use the core functionality programmatically.
"""

import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core import Config, TemplateCatalog, SequenceManager
from core.models import ExecutorConfig


def example_load_catalog():
    """Example: Load and explore the template catalog."""
    print("=== Loading Template Catalog ===")
    
    # Load the catalog
    catalog = TemplateCatalog.load_catalog()
    
    if not catalog:
        print("No catalog found. Make sure templates are properly configured.")
        return
    
    print(f"Loaded catalog with {len(catalog.get('templates', {}))} templates")
    print(f"Found {len(catalog.get('sequences', {}))} sequences")
    
    # List available sequences
    sequences = TemplateCatalog.get_all_sequences(catalog)
    print(f"\nAvailable sequences: {sequences[:10]}...")  # Show first 10
    
    return catalog


def example_get_template():
    """Example: Get a specific template."""
    print("\n=== Getting Specific Template ===")
    
    catalog = TemplateCatalog.load_catalog()
    if not catalog:
        return
    
    # Try to get a specific template
    template_id = "1031-a-form_classifier"
    template = TemplateCatalog.get_template(catalog, template_id)
    
    if template:
        print(f"Found template: {template_id}")
        parts = template.get("parts", {})
        print(f"Title: {parts.get('title', 'N/A')}")
        print(f"Keywords: {parts.get('keywords', 'N/A')}")
    else:
        print(f"Template {template_id} not found")


def example_get_sequence():
    """Example: Get a sequence of templates."""
    print("\n=== Getting Template Sequence ===")
    
    catalog = TemplateCatalog.load_catalog()
    if not catalog:
        return
    
    # Try to get a sequence
    sequence_id = "1031"
    sequence_steps = TemplateCatalog.get_sequence(catalog, sequence_id)
    
    if sequence_steps:
        print(f"Found sequence {sequence_id} with {len(sequence_steps)} steps:")
        for step_id, template_data in sequence_steps:
            title = template_data.get("parts", {}).get("title", "Unknown")
            print(f"  Step {step_id}: {title}")
    else:
        print(f"Sequence {sequence_id} not found")


def example_parse_sequence_spec():
    """Example: Parse complex sequence specifications."""
    print("\n=== Parsing Sequence Specifications ===")
    
    test_specs = [
        "1031",
        "1031:a-c", 
        "1031|9000",
        "1031:a-c|9000:a-b"
    ]
    
    for spec in test_specs:
        parsed = SequenceManager.parse_sequence_spec(spec)
        print(f"'{spec}' -> {parsed}")


def example_model_configuration():
    """Example: Explore model configuration."""
    print("\n=== Model Configuration ===")
    
    # Show available models
    models = Config.get_available_models()
    
    for provider, provider_models in models.items():
        print(f"\n{provider.upper()} models:")
        for model_info in provider_models[:3]:  # Show first 3
            default_marker = " (default)" if model_info["is_default"] else ""
            print(f"  - {model_info['name']}{default_marker}")
    
    # Show default model for each provider
    print(f"\nDefault models:")
    for provider in Config.PROVIDERS:
        default = Config.get_default_model(provider)
        print(f"  {provider}: {default}")


def main():
    """Run all examples."""
    print("AI Systems Template Framework - Basic Usage Examples")
    print("=" * 60)
    
    try:
        example_load_catalog()
        example_get_template()
        example_get_sequence()
        example_parse_sequence_spec()
        example_model_configuration()
        
        print("\n" + "=" * 60)
        print("Examples completed successfully!")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
