"""
Example workflow demonstrating the new stage-based template system.

This script shows how to:
1. Create stage1 prototypes with automatic ID assignment
2. Promote templates through the lifecycle stages
3. Use the simplified API for common operations
"""

import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import templates


def demonstrate_stage1_prototyping():
    """Demonstrate rapid prototyping in stage1."""
    print("=== Stage 1: Rapid Prototyping ===")
    
    # Create individual prototypes with automatic ID assignment
    print("\n1. Creating individual prototypes:")
    
    try:
        # Create an amplifier prototype
        amp_id = templates.create_amplifier("Emotional Intensifier", "emotional_resonance")
        print(f"✅ Created amplifier: {amp_id}")
        
        # Create a clarifier prototype
        clar_id = templates.create_clarifier("Concept Illuminator", "conceptual_structure")
        print(f"✅ Created clarifier: {clar_id}")
        
        # Create a reducer prototype
        red_id = templates.create_reducer("Essence Distiller", "core_meaning")
        print(f"✅ Created reducer: {red_id}")
        
    except Exception as e:
        print(f"❌ Error creating prototypes: {e}")
        return []
    
    # Create a sequence with automatic step assignment
    print("\n2. Creating a prototype sequence:")
    
    try:
        sequence_templates = [
            {"title": "Input Analyzer", "target": "raw_content"},
            {"title": "Pattern Extractor", "target": "structural_patterns"},
            {"title": "Output Formatter", "target": "final_structure"}
        ]
        
        seq_id = templates.create_sequence("formatters", sequence_templates)
        print(f"✅ Created sequence: {seq_id} (steps a, b, c)")
        
    except Exception as e:
        print(f"❌ Error creating sequence: {e}")
        seq_id = None
    
    # Show what we created
    print("\n3. Stage1 templates created:")
    stage1_templates = templates.list_templates_by_stage("stage1")
    for template_id in stage1_templates[-5:]:  # Show last 5
        print(f"   {template_id}")
    
    return [amp_id, clar_id, red_id, seq_id] if seq_id else [amp_id, clar_id, red_id]


def demonstrate_stage2_promotion(stage1_templates):
    """Demonstrate promotion to stage2."""
    print("\n=== Stage 2: Candidate Management ===")
    
    if not stage1_templates:
        print("No stage1 templates to promote")
        return []
    
    promoted_templates = []
    
    for template_id in stage1_templates[:2]:  # Promote first 2
        try:
            print(f"\n1. Promoting {template_id} to stage2:")
            
            # Show placement suggestions
            suggestions = templates.suggest_placement(template_id)
            print("   Placement suggestions:")
            for suggestion in suggestions:
                print(f"     - ID {suggestion['id']}: {suggestion['reason']} ({suggestion['confidence']:.1%})")
            
            # Auto-promote with first suggestion
            stage2_id = templates.promote_to_stage2(template_id, auto_accept=True)
            print(f"✅ Promoted to stage2: {stage2_id}")
            promoted_templates.append(stage2_id)
            
        except Exception as e:
            print(f"❌ Error promoting {template_id}: {e}")
    
    # Show stage2 templates
    print("\n2. Stage2 templates:")
    stage2_templates = templates.list_templates_by_stage("stage2")
    for template_id in stage2_templates[-3:]:  # Show last 3
        print(f"   {template_id}")
    
    return promoted_templates


def demonstrate_stage3_production(stage2_templates):
    """Demonstrate promotion to stage3."""
    print("\n=== Stage 3: Production Management ===")
    
    if not stage2_templates:
        print("No stage2 templates to promote")
        return []
    
    # Show production architecture guidance
    templates.show_production_architecture()
    templates.suggest_production_ranges()
    
    # For demo purposes, we'll use manual placement with predetermined IDs
    production_templates = []
    demo_production_ids = [3001, 3002]  # Predetermined for demo
    
    for i, template_id in enumerate(stage2_templates[:2]):
        try:
            target_id = demo_production_ids[i]
            print(f"\n1. Promoting {template_id} to production at ID {target_id}:")
            
            stage3_id = templates.manual_placement(template_id, target_id)
            print(f"✅ Promoted to production: {stage3_id}")
            production_templates.append(stage3_id)
            
        except Exception as e:
            print(f"❌ Error promoting {template_id}: {e}")
    
    # Show production templates
    print("\n2. Production templates:")
    stage3_templates = templates.list_templates_by_stage("stage3")
    for template_id in stage3_templates[-3:]:  # Show last 3
        print(f"   {template_id}")
    
    return production_templates


def demonstrate_simplified_api():
    """Demonstrate the simplified API functions."""
    print("\n=== Simplified API Demonstration ===")
    
    # Show stage information
    print("\n1. Stage information:")
    for stage in ["stage1", "stage2", "stage3"]:
        info = templates.get_stage_info(stage)
        print(f"   {stage}: {info['range']} - {info['description']}")
        print(f"      Auto-ID: {info['auto_id']}")
    
    # Create prototype using simplified API
    print("\n2. Using simplified create_prototype API:")
    try:
        proto_id = templates.create_prototype("transformer", "Data Metamorphosis", "data_structure")
        print(f"✅ Created prototype: {proto_id}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Show template counts by stage
    print("\n3. Template counts by stage:")
    for stage in ["stage1", "stage2", "stage3"]:
        templates_in_stage = templates.list_templates_by_stage(stage)
        print(f"   {stage}: {len(templates_in_stage)} templates")


def demonstrate_catalog_integration():
    """Demonstrate catalog integration with stage awareness."""
    print("\n=== Catalog Integration ===")
    
    try:
        # Regenerate catalog with stage awareness
        catalog = templates.regenerate_catalog()
        
        print(f"1. Catalog metadata:")
        meta = catalog.get("catalog_meta", {})
        print(f"   Total templates: {meta.get('total_templates', 0)}")
        print(f"   Total sequences: {meta.get('total_sequences', 0)}")
        print(f"   Generated at: {meta.get('generated_at', 'unknown')}")
        
        # Show stage distribution
        print(f"\n2. Stage distribution:")
        stage_counts = {"stage1": 0, "stage2": 0, "stage3": 0, "unknown": 0}
        
        for template_id in catalog.get("templates", {}):
            config = templates.TemplateConfigMD()
            stage = config.get_template_stage(template_id)
            stage_counts[stage] = stage_counts.get(stage, 0) + 1
        
        for stage, count in stage_counts.items():
            if count > 0:
                print(f"   {stage}: {count} templates")
        
    except Exception as e:
        print(f"❌ Error with catalog: {e}")


def main():
    """Run the complete workflow demonstration."""
    print("AI Systems Template Framework - Stage-Based Workflow Demo")
    print("=" * 60)
    
    try:
        # Stage 1: Rapid prototyping
        stage1_templates = demonstrate_stage1_prototyping()
        
        # Stage 2: Candidate management
        stage2_templates = demonstrate_stage2_promotion(stage1_templates)
        
        # Stage 3: Production management
        stage3_templates = demonstrate_stage3_production(stage2_templates)
        
        # Simplified API
        demonstrate_simplified_api()
        
        # Catalog integration
        demonstrate_catalog_integration()
        
        print("\n" + "=" * 60)
        print("✅ Workflow demonstration completed successfully!")
        print("\nKey benefits demonstrated:")
        print("  • Automatic ID assignment for rapid prototyping")
        print("  • Guided promotion between lifecycle stages")
        print("  • Manual precision control for production placement")
        print("  • Simplified API for common operations")
        print("  • Stage-aware catalog integration")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
