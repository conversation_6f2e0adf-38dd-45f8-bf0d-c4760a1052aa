"""
Example workflow demonstrating the new stage-based template system.

This script shows how to:
1. Create stage1 prototypes with automatic ID assignment
2. Promote templates through the lifecycle stages
3. Use the simplified API for common operations
"""

import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import templates


def demonstrate_stage1_creation():
    """Demonstrate direct template creation in stage1."""
    print("=== Stage 1: Template Creation ===")

    # Example template specifications (your format)
    my_templates = {
        "reflection_initializer": {
            "interpretation": "Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user's evolving thought.",
            "transformation": "`{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract_existential_frame(), identify_signal_from_friction(), prepare_resonant_interface()]; constraints=[avoid_topical_reduction(), refuse_shallow_classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal_recursion()]; output={meta_frame:str}}`",
            "testprompt": '"I keep returning to the same question—not because I expect a new answer, but because I need to hear myself ask it again."'
        },
        "directional_translator": {
            "interpretation": "Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context.",
            "transformation": "`{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract_directional_axis(), validate_against_kuci_modelex(), return_directional_instruction()]; constraints=[context_agnostic_processing(), preserve_ambiguity_where_intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`",
            "testprompt": 'Amplify this: "i don\'t know if i\'m waiting for something to begin, or just afraid to admit it already has."'
        },
        "signal_resonator": {
            "interpretation": "Your goal is not to **respond to** the prompt directly, but to **resonate** with it—amplifying or attenuating its latent structure to surface clarity through tension or refinement.",
            "transformation": "`{role=signal_resonator; input=[signal:str, modulation:str]; process=[analyze_emotional_topology(), apply_modulation_direction(), surface_hidden_structure(), recompose_at_aligned_amplitude()]; constraints=[modulation_must_preserve_integrity(), ambiguity_must_be_honored_where_functional()]; requirements=[resonant_output(), clarity_through_friction()]; output={resonated:str}}`",
            "testprompt": 'Resonate this at higher pressure: "every time i try to be honest, the silence that follows feels like proof that i shouldn\'t have spoken."'
        }
    }

    print("\n1. Creating templates from exact specifications:")

    try:
        # Create all templates at once
        results = templates.create_batch(my_templates)

        for name, template_id in results.items():
            print(f"[OK] Created {name}: {template_id}")

        return list(results.values())

    except Exception as e:
        print(f"[ERROR] Error creating templates: {e}")
        return []


def demonstrate_stage2_promotion(stage1_templates):
    """Demonstrate promotion to stage2."""
    print("\n=== Stage 2: Candidate Management ===")
    
    if not stage1_templates:
        print("No stage1 templates to promote")
        return []
    
    promoted_templates = []
    
    for template_id in stage1_templates[:2]:  # Promote first 2
        try:
            print(f"\n1. Promoting {template_id} to stage2:")
            
            # Show placement suggestions
            suggestions = templates.suggest_placement(template_id)
            print("   Placement suggestions:")
            for suggestion in suggestions:
                print(f"     - ID {suggestion['id']}: {suggestion['reason']} ({suggestion['confidence']:.1%})")
            
            # Auto-promote with first suggestion
            stage2_id = templates.promote_to_stage2(template_id, auto_accept=True)
            print(f"[OK] Promoted to stage2: {stage2_id}")
            promoted_templates.append(stage2_id)

        except Exception as e:
            print(f"[ERROR] Error promoting {template_id}: {e}")
    
    # Show stage2 templates
    print("\n2. Stage2 templates:")
    stage2_templates = templates.list_templates_by_stage("stage2")
    for template_id in stage2_templates[-3:]:  # Show last 3
        print(f"   {template_id}")
    
    return promoted_templates


def demonstrate_stage3_production(stage2_templates):
    """Demonstrate promotion to stage3."""
    print("\n=== Stage 3: Production Management ===")
    
    if not stage2_templates:
        print("No stage2 templates to promote")
        return []
    
    # Show production architecture guidance
    templates.show_production_architecture()
    templates.suggest_production_ranges()
    
    # For demo purposes, we'll use manual placement with predetermined IDs
    production_templates = []
    demo_production_ids = [3001, 3002]  # Predetermined for demo
    
    for i, template_id in enumerate(stage2_templates[:2]):
        try:
            target_id = demo_production_ids[i]
            print(f"\n1. Promoting {template_id} to production at ID {target_id}:")
            
            stage3_id = templates.manual_placement(template_id, target_id)
            print(f"[OK] Promoted to production: {stage3_id}")
            production_templates.append(stage3_id)

        except Exception as e:
            print(f"[ERROR] Error promoting {template_id}: {e}")
    
    # Show production templates
    print("\n2. Production templates:")
    stage3_templates = templates.list_templates_by_stage("stage3")
    for template_id in stage3_templates[-3:]:  # Show last 3
        print(f"   {template_id}")
    
    return production_templates


def demonstrate_simplified_api():
    """Demonstrate the simplified API functions."""
    print("\n=== Simplified API Demonstration ===")
    
    # Show stage information
    print("\n1. Stage information:")
    for stage in ["stage1", "stage2", "stage3"]:
        info = templates.get_stage_info(stage)
        print(f"   {stage}: {info['range']} - {info['description']}")
        print(f"      Auto-ID: {info['auto_id']}")
    
    # Create template using simplified API
    print("\n2. Using simplified create_template API:")
    try:
        proto_id = templates.create_template(
            name="data_metamorphosis",
            interpretation="Your goal is not to **modify** the data, but to **transform** it into a fundamentally different structural form.",
            transformation="`{role=data_transformer; input=[data:any]; process=[analyze_current_structure(), determine_target_form(), execute_metamorphosis(), validate_transformation()]; constraints=[preserve_essential_information(), maintain_data_integrity()]; requirements=[complete_transformation(), structural_coherence()]; output={transformed_data:any}}`"
        )
        print(f"[OK] Created template: {proto_id}")
    except Exception as e:
        print(f"[ERROR] Error: {e}")
    
    # Show template counts by stage
    print("\n3. Template counts by stage:")
    for stage in ["stage1", "stage2", "stage3"]:
        templates_in_stage = templates.list_templates_by_stage(stage)
        print(f"   {stage}: {len(templates_in_stage)} templates")


def demonstrate_catalog_integration():
    """Demonstrate catalog integration with stage awareness."""
    print("\n=== Catalog Integration ===")
    
    try:
        # Regenerate catalog with stage awareness
        catalog = templates.regenerate_catalog()
        
        print(f"1. Catalog metadata:")
        meta = catalog.get("catalog_meta", {})
        print(f"   Total templates: {meta.get('total_templates', 0)}")
        print(f"   Total sequences: {meta.get('total_sequences', 0)}")
        print(f"   Generated at: {meta.get('generated_at', 'unknown')}")
        
        # Show stage distribution
        print(f"\n2. Stage distribution:")
        stage_counts = {"stage1": 0, "stage2": 0, "stage3": 0, "unknown": 0}
        
        for template_id in catalog.get("templates", {}):
            config = templates.TemplateConfigMD()
            stage = config.get_template_stage(template_id)
            stage_counts[stage] = stage_counts.get(stage, 0) + 1
        
        for stage, count in stage_counts.items():
            if count > 0:
                print(f"   {stage}: {count} templates")
        
    except Exception as e:
        print(f"[ERROR] Error with catalog: {e}")


def main():
    """Run the complete workflow demonstration."""
    print("AI Systems Template Framework - Stage-Based Workflow Demo")
    print("=" * 60)
    
    try:
        # Stage 1: Template creation
        stage1_templates = demonstrate_stage1_creation()
        
        # Stage 2: Candidate management
        stage2_templates = demonstrate_stage2_promotion(stage1_templates)
        
        # Stage 3: Production management
        stage3_templates = demonstrate_stage3_production(stage2_templates)
        
        # Simplified API
        demonstrate_simplified_api()
        
        # Catalog integration
        demonstrate_catalog_integration()
        
        print("\n" + "=" * 60)
        print("[SUCCESS] Workflow demonstration completed successfully!")
        print("\nKey benefits demonstrated:")
        print("  - Automatic ID assignment for rapid prototyping")
        print("  - Guided promotion between lifecycle stages")
        print("  - Manual precision control for production placement")
        print("  - Simplified API for common operations")
        print("  - Stage-aware catalog integration")

    except Exception as e:
        print(f"\n[FAILED] Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
