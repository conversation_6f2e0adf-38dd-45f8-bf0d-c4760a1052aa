"""
Stage 1 prototype generation with automatic ID assignment.

Enables frictionless template creation for experimentation and testing.
"""

import os
import re
import glob
from typing import Dict, List, Any
from .catalog import SEMANTIC_CATEGORIES, LIFECYCLE_STAGES, generate_catalog, TemplateConfigMD


class PrototypeGenerator:
    """Automatic template generation for stage1 prototyping."""
    
    def __init__(self, templates_dir="md"):
        self.templates_dir = templates_dir
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.full_templates_dir = os.path.join(self.script_dir, templates_dir)
    
    def create_template(self, category: str, title: str, target: str = None) -> str:
        """Create prototype template with automatic ID assignment."""
        template_id = self._get_next_available_id(category)
        
        # Generate template content based on category
        content = self._generate_template_content(category, title, target)
        
        # Save to file
        filename = f"{template_id}-{self._slugify(title)}.md"
        filepath = os.path.join(self.full_templates_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Created prototype template: {template_id} -> {filename}")
        return str(template_id)
    
    def create_sequence(self, category: str, templates: List[Dict[str, str]]) -> str:
        """Create complete sequence with automatic step assignment."""
        sequence_id = self._get_next_available_id(category)
        
        for i, template_data in enumerate(templates):
            step_letter = chr(ord('a') + i)
            template_id = f"{sequence_id}-{step_letter}"
            
            # Create individual template with specific ID
            content = self._generate_template_content(
                category, 
                template_data["title"], 
                template_data.get("target")
            )
            
            filename = f"{template_id}-{self._slugify(template_data['title'])}.md"
            filepath = os.path.join(self.full_templates_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"Created sequence step: {template_id} -> {filename}")
        
        return str(sequence_id)
    
    def _get_next_available_id(self, category: str) -> int:
        """Find next available ID in category's sub-range."""
        if category not in SEMANTIC_CATEGORIES:
            raise ValueError(f"Unknown category: {category}")
        
        category_config = SEMANTIC_CATEGORIES[category]
        stage1_config = LIFECYCLE_STAGES["stage1"]
        
        start_id = stage1_config.range_start + category_config["offset"]
        end_id = start_id + 99  # 100 IDs per category
        
        existing_ids = self._get_existing_ids_in_range(start_id, end_id)
        
        for candidate_id in range(start_id, end_id + 1):
            if candidate_id not in existing_ids:
                return candidate_id
        
        raise ValueError(f"No available IDs in {category} range ({start_id}-{end_id})")
    
    def _get_existing_ids_in_range(self, start: int, end: int) -> set:
        """Get all existing template IDs in specified range."""
        existing_ids = set()
        
        # Scan template files for existing IDs
        pattern = os.path.join(self.full_templates_dir, "*.md")
        for filepath in glob.glob(pattern):
            filename = os.path.basename(filepath)
            try:
                numeric_id = int(filename.split('-')[0])
                if start <= numeric_id <= end:
                    existing_ids.add(numeric_id)
            except (ValueError, IndexError):
                continue
        
        return existing_ids
    
    def _generate_template_content(self, category: str, title: str, target: str) -> str:
        """Generate template content based on category patterns."""
        category_config = SEMANTIC_CATEGORIES[category]
        direction = category_config["direction"]
        
        # Category-specific template patterns
        patterns = {
            "amplifiers": self._amplifier_pattern,
            "builders": self._builder_pattern,
            "clarifiers": self._clarifier_pattern,
            "formatters": self._formatter_pattern,
            "identifiers": self._identifier_pattern,
            "optimizers": self._optimizer_pattern,
            "reducers": self._reducer_pattern,
            "transformers": self._transformer_pattern,
            "translators": self._translator_pattern
        }
        
        return patterns[category](title, target or "content", direction)
    
    def _amplifier_pattern(self, title: str, target: str, direction: str) -> str:
        """Generate amplifier template pattern."""
        interpretation = f"Your goal is not to **analyze** the {target}, but to **amplify** its inherent qualities through intensification."
        transformation = f"`{{role=amplification_operator; input=[content:any]; process=[identify_core_qualities(), intensify_{target.replace(' ', '_')}_characteristics(), magnify_essential_elements(), enhance_natural_patterns()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; requirements=[amplified_output(), enhanced_potency()]; output={{amplified:any}}}}`"
        return f"[{title}] {interpretation} Execute as: {transformation}"
    
    def _builder_pattern(self, title: str, target: str, direction: str) -> str:
        """Generate builder template pattern."""
        interpretation = f"Your goal is not to **modify** existing {target}, but to **build** new structures from foundational elements."
        transformation = f"`{{role=construction_operator; input=[elements:any]; process=[analyze_foundation(), design_structure(), construct_{target.replace(' ', '_')}(), validate_integrity()]; constraints=[structural_soundness(), component_compatibility()]; requirements=[complete_construction(), functional_output()]; output={{built:any}}}}`"
        return f"[{title}] {interpretation} Execute as: {transformation}"
    
    def _clarifier_pattern(self, title: str, target: str, direction: str) -> str:
        """Generate clarifier template pattern."""
        interpretation = f"Your goal is not to **explain** the {target}, but to **clarify** its inherent structure through transparency enhancement."
        transformation = f"`{{role=clarification_operator; input=[content:any]; process=[remove_obscuring_elements(), enhance_natural_transparency(), sharpen_definition_boundaries(), illuminate_{target.replace(' ', '_')}_structure()]; constraints=[preserve_original_meaning(), maintain_authentic_form()]; requirements=[crystal_clarity(), enhanced_understanding()]; output={{clarified:any}}}}`"
        return f"[{title}] {interpretation} Execute as: {transformation}"
    
    def _formatter_pattern(self, title: str, target: str, direction: str) -> str:
        """Generate formatter template pattern."""
        interpretation = f"Your goal is not to **change** the {target}, but to **format** it into optimal structural organization."
        transformation = f"`{{role=formatting_operator; input=[content:any]; process=[analyze_current_structure(), determine_optimal_format(), apply_{target.replace(' ', '_')}_formatting(), validate_organization()]; constraints=[preserve_all_content(), maintain_logical_flow()]; requirements=[structured_output(), enhanced_readability()]; output={{formatted:any}}}}`"
        return f"[{title}] {interpretation} Execute as: {transformation}"
    
    def _identifier_pattern(self, title: str, target: str, direction: str) -> str:
        """Generate identifier template pattern."""
        interpretation = f"Your goal is not to **interpret** the content, but to **identify** the specific {target} elements within it."
        transformation = f"`{{role=identification_operator; input=[content:any]; process=[scan_for_{target.replace(' ', '_')}_patterns(), isolate_target_elements(), classify_identified_items(), validate_identification()]; constraints=[focus_on_identification_only(), ignore_interpretation()]; requirements=[precise_identification(), clear_classification()]; output={{identified:list}}}}`"
        return f"[{title}] {interpretation} Execute as: {transformation}"
    
    def _optimizer_pattern(self, title: str, target: str, direction: str) -> str:
        """Generate optimizer template pattern."""
        interpretation = f"Your goal is not to **change** the {target}, but to **optimize** its efficiency and effectiveness."
        transformation = f"`{{role=optimization_operator; input=[content:any]; process=[analyze_current_efficiency(), identify_optimization_opportunities(), apply_{target.replace(' ', '_')}_improvements(), measure_optimization_gains()]; constraints=[preserve_core_functionality(), maintain_compatibility()]; requirements=[improved_efficiency(), measurable_enhancement()]; output={{optimized:any}}}}`"
        return f"[{title}] {interpretation} Execute as: {transformation}"
    
    def _reducer_pattern(self, title: str, target: str, direction: str) -> str:
        """Generate reducer template pattern."""
        interpretation = f"Your goal is not to **remove** from the {target}, but to **reduce** it to its essential core elements."
        transformation = f"`{{role=reduction_operator; input=[content:any]; process=[identify_essential_elements(), eliminate_redundant_components(), compress_{target.replace(' ', '_')}_density(), preserve_core_functionality()]; constraints=[zero_essential_loss(), maintain_completeness()]; requirements=[maximum_compression(), preserved_essence()]; output={{reduced:any}}}}`"
        return f"[{title}] {interpretation} Execute as: {transformation}"
    
    def _transformer_pattern(self, title: str, target: str, direction: str) -> str:
        """Generate transformer template pattern."""
        interpretation = f"Your goal is not to **modify** the {target}, but to **transform** it into a fundamentally different form."
        transformation = f"`{{role=transformation_operator; input=[content:any]; process=[analyze_current_form(), determine_target_transformation(), execute_{target.replace(' ', '_')}_metamorphosis(), validate_transformation()]; constraints=[preserve_essential_properties(), ensure_transformation_completeness()]; requirements=[fundamental_change(), maintained_coherence()]; output={{transformed:any}}}}`"
        return f"[{title}] {interpretation} Execute as: {transformation}"
    
    def _translator_pattern(self, title: str, target: str, direction: str) -> str:
        """Generate translator template pattern."""
        interpretation = f"Your goal is not to **interpret** the {target}, but to **translate** it between different representations or formats."
        transformation = f"`{{role=translation_operator; input=[content:any]; process=[analyze_source_format(), determine_target_format(), execute_{target.replace(' ', '_')}_translation(), validate_translation_accuracy()]; constraints=[preserve_meaning(), maintain_fidelity()]; requirements=[accurate_translation(), format_compliance()]; output={{translated:any}}}}`"
        return f"[{title}] {interpretation} Execute as: {transformation}"
    
    def _slugify(self, text: str) -> str:
        """Convert text to URL-friendly slug."""
        # Convert to lowercase and replace spaces/special chars with hyphens
        slug = re.sub(r'[^\w\s-]', '', text.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')


# Simplified creation functions for easy import
def create_amplifier(title: str, target: str = "inherent_qualities") -> str:
    """Create amplifier prototype with automatic ID."""
    generator = PrototypeGenerator()
    return generator.create_template("amplifiers", title, target)

def create_builder(title: str, target: str = "new_content") -> str:
    """Create builder prototype with automatic ID."""
    generator = PrototypeGenerator()
    return generator.create_template("builders", title, target)

def create_clarifier(title: str, target: str = "structure") -> str:
    """Create clarifier prototype with automatic ID."""
    generator = PrototypeGenerator()
    return generator.create_template("clarifiers", title, target)

def create_formatter(title: str, target: str = "content") -> str:
    """Create formatter prototype with automatic ID."""
    generator = PrototypeGenerator()
    return generator.create_template("formatters", title, target)

def create_identifier(title: str, target: str = "elements") -> str:
    """Create identifier prototype with automatic ID."""
    generator = PrototypeGenerator()
    return generator.create_template("identifiers", title, target)

def create_optimizer(title: str, target: str = "performance") -> str:
    """Create optimizer prototype with automatic ID."""
    generator = PrototypeGenerator()
    return generator.create_template("optimizers", title, target)

def create_reducer(title: str, target: str = "content") -> str:
    """Create reducer prototype with automatic ID."""
    generator = PrototypeGenerator()
    return generator.create_template("reducers", title, target)

def create_transformer(title: str, target: str = "form") -> str:
    """Create transformer prototype with automatic ID."""
    generator = PrototypeGenerator()
    return generator.create_template("transformers", title, target)

def create_translator(title: str, target: str = "format") -> str:
    """Create translator prototype with automatic ID."""
    generator = PrototypeGenerator()
    return generator.create_template("translators", title, target)

def create_sequence(category: str, templates: List[Dict[str, str]]) -> str:
    """Create complete sequence with automatic step assignment."""
    generator = PrototypeGenerator()
    return generator.create_sequence(category, templates)

def auto_generate_id(category: str) -> int:
    """Get next available ID for a category without creating template."""
    generator = PrototypeGenerator()
    return generator._get_next_available_id(category)
