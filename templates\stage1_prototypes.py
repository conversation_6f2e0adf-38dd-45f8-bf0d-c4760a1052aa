"""
Stage 1: Template Creation

Direct transformation: specification → template
Enables frictionless template creation from exact specifications.
"""

import os
import re
import glob
from typing import Dict
from .catalog import LIFECYCLE_STAGES


def create_template(name: str, interpretation: str, transformation: str, testprompt: str = "") -> str:
    """Create stage1 template from the three canonical parts."""
    template_id = _get_next_available_id()
    title = name.replace('_', ' ').title()

    content = f"[{title}] {interpretation} Execute as: {transformation}"
    filename = f"{template_id}-{name}.md"

    _save_template(filename, content, testprompt)
    print(f"Created template: {template_id} -> {filename}")
    return str(template_id)


def create_batch(templates: Dict[str, Dict[str, str]]) -> Dict[str, str]:
    """Create multiple templates from specification dict."""
    results = {}
    for name, spec in templates.items():
        template_id = create_template(
            name=name,
            interpretation=spec["interpretation"],
            transformation=spec["transformation"],
            testprompt=spec.get("testprompt", "")
        )
        results[name] = template_id
    return results


def _get_next_available_id() -> int:
    """Find next available ID in stage1 range."""
    stage1_config = LIFECYCLE_STAGES["stage1"]
    start_id = stage1_config.range_start
    end_id = stage1_config.range_end

    existing_ids = _get_existing_ids_in_range(start_id, end_id)

    for candidate_id in range(start_id, end_id + 1):
        if candidate_id not in existing_ids:
            return candidate_id

    raise ValueError(f"No available IDs in stage1 range ({start_id}-{end_id})")


def _get_existing_ids_in_range(start: int, end: int) -> set:
    """Get all existing template IDs in specified range from stage1 directory."""
    existing_ids = set()

    # Get stage1 directory path
    script_dir = os.path.dirname(os.path.abspath(__file__))
    stage1_dir = os.path.join(script_dir, "md", "stage1")

    # Scan stage1 template files for existing IDs
    if os.path.exists(stage1_dir):
        pattern = os.path.join(stage1_dir, "*.md")
        for filepath in glob.glob(pattern):
            filename = os.path.basename(filepath)
            try:
                numeric_id = int(filename.split('-')[0])
                if start <= numeric_id <= end:
                    existing_ids.add(numeric_id)
            except (ValueError, IndexError):
                continue

    return existing_ids


def _save_template(filename: str, content: str, testprompt: str = ""):
    """Save template to stage1 directory with optional test prompt metadata."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    stage1_dir = os.path.join(script_dir, "md", "stage1")
    filepath = os.path.join(stage1_dir, filename)

    # Ensure stage1 directory exists
    os.makedirs(stage1_dir, exist_ok=True)

    # Write template content
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

    # Save test prompt as companion file if provided
    if testprompt:
        testprompt_filename = filename.replace('.md', '.testprompt')
        testprompt_filepath = os.path.join(stage1_dir, testprompt_filename)
        with open(testprompt_filepath, 'w', encoding='utf-8') as f:
            f.write(testprompt)


def _slugify(text: str) -> str:
    """Convert text to URL-friendly slug."""
    slug = re.sub(r'[^\w\s-]', '', text.lower())
    slug = re.sub(r'[-\s]+', '-', slug)
    return slug.strip('-')
# Clean, simple API - no complex pattern generators needed
