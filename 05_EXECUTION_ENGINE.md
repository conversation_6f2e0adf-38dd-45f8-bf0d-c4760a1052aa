# Execution Engine Documentation

## Overview

The Execution Engine (`lvl1_sequence_executor.py`) is the core component that orchestrates the execution of template sequences across multiple LLM models. It provides a comprehensive framework for running AI instruction workflows with advanced features like streaming output, cost tracking, and multi-model support.

## Core Architecture

### Main Components

#### 1. **TemplateCatalog** - Template Management System
- **Purpose**: Manages template catalogs from different levels and sources
- **Features**: Dynamic module registration, catalog merging, template discovery
- **API**: `load_catalog()`, `get_sequence()`, `get_template()`, `regenerate_catalog()`

#### 2. **SequenceManager** - Sequence Resolution & Validation
- **Purpose**: Resolves complex sequence specifications and validates templates
- **Features**: Advanced sequence parsing, template validation, keyword-based selection
- **Capabilities**: Range selection, filtering, multi-sequence composition

#### 3. **ExecutorConfig** - Configuration Management
- **Purpose**: Centralized configuration for execution parameters
- **Components**: Sequence steps, models, output settings, execution options
- **Validation**: Pydantic-based type checking and validation

#### 4. **StreamingJSONWriter** - Output Management
- **Purpose**: Handles structured JSON output with real-time streaming
- **Features**: Incremental writing, proper JSON formatting, cost tracking integration

## Execution Modes

### 1. Catalog Mode (Default)
Loads templates from markdown files and generates dynamic catalogs.

```bash
python lvl1_sequence_executor.py --sequence 0001 --models gpt-4o-openai "What is AI?"
```

**Features:**
- Automatic template discovery
- Metadata extraction
- Sequence organization
- Complex sequence specifications

**Sequence Specifications:**
- `0001` - Execute entire sequence 0001
- `0001:a-c` - Execute steps a through c of sequence 0001
- `0001|0002` - Execute sequences 0001 and 0002
- `keyword:distill` - Execute templates containing "distill" keyword

### 2. Text Mode
Loads sequences from plain text files with instructions separated by `---`.

```bash
python lvl1_sequence_executor.py --use-text --sequence mysequence.txt "Analyze this text"
```

**Features:**
- Simple text-based format
- Quick prototyping
- No metadata requirements
- Direct instruction execution

### 3. Chain Mode
Output from step N becomes input to step N+1, enabling complex transformations.

```bash
python lvl1_sequence_executor.py --sequence 0002 --chain-mode "Raw input text"
```

**Features:**
- Sequential processing
- Context preservation
- Multi-step refinement
- Original prompt inclusion

## Command Line Interface

### Basic Usage
```bash
python lvl1_sequence_executor.py [OPTIONS] "USER_PROMPT"
```

### Core Arguments

#### **--sequence** (Sequence Specification)
Defines which templates to execute.
```bash
--sequence 0001                    # Single sequence
--sequence 0001:a-c               # Range selection
--sequence 0001|0002              # Multiple sequences
--sequence keyword:distill        # Keyword-based selection
```

#### **--models** (Model Selection)
Specifies which LLM models to use.
```bash
--models "gpt-4o-openai,claude-3-sonnet"
--models "gpt-3.5-turbo"
```

#### **--provider** (Provider Selection)
Quick model selection by provider.
```bash
--provider openai                 # Use default OpenAI model
--provider anthropic              # Use default Anthropic model
```

### Output Control

#### **--output-dir** (Output Directory)
```bash
--output-dir ./results            # Custom output directory
```

#### **--output-file** (Specific Output File)
```bash
--output-file analysis.json       # Custom filename
```

#### **--minified-output** (Compact Format)
```bash
--minified-output                 # Minified JSON output
```

### Display Options

#### **--show-inputs** / **--hide-inputs**
```bash
--hide-inputs                     # Don't display input prompts
```

#### **--show-system-instructions** / **--hide-system-instructions**
```bash
--hide-system-instructions        # Don't display system prompts
```

#### **--show-responses** / **--hide-responses**
```bash
--hide-responses                  # Don't display LLM responses
```

### Execution Options

#### **--chain-mode** / **--no-chain-mode**
```bash
--chain-mode                      # Enable sequential processing
--no-chain-mode                   # Independent step execution
```

#### **--use-text**
```bash
--use-text                        # Use text-based sequences
```

### Advanced Features

#### **--aggregator** (Aggregation Templates)
```bash
--aggregator 0100                 # Use template 0100 as aggregator
--aggregator-inputs "a,b,c"       # Aggregate specific steps
```

#### **--force-regenerate**
```bash
--force-regenerate                # Force catalog regeneration
```

### Information Commands

#### **--list-models**
```bash
--list-models                     # Show available models
```

#### **--list-sequences**
```bash
--list-sequences                  # Show available sequences
```

## Model Configuration

### Supported Providers

#### **OpenAI**
- `gpt-3.5-turbo`
- `gpt-4`
- `gpt-4o`
- `gpt-4o-mini`

#### **Anthropic**
- `claude-3-haiku`
- `claude-3-sonnet`
- `claude-3-opus`
- `claude-3-5-sonnet`

#### **Google**
- `gemini-pro`
- `gemini-1.5-pro`

#### **Local Models**
- Any model supported by LiteLLM proxy

### Model Mapping
The system uses friendly names that map to actual model identifiers:
```python
MODEL_MAPPING = {
    "gpt-4o-openai": "gpt-4o",
    "claude-3-sonnet": "claude-3-sonnet-20240229",
    "gemini-pro": "gemini/gemini-pro"
}
```

### Configuration Options
```bash
--temperature 0.7                 # Set temperature (0.0-2.0)
--max-tokens 2000                 # Set max tokens
```

## Output Format

### Structured JSON Output
```json
{
  "execution_metadata": {
    "initial_prompt": "User input",
    "sequence_id": "0001",
    "timestamp": "2024-01-01T12:00:00Z",
    "total_cost": 0.05
  },
  "results": [
    {
      "step": "a",
      "step_id": "0001-a-template",
      "title": "Template Title",
      "system_instruction": "Full system prompt",
      "input": "Input for this step",
      "responses": [
        {
          "model": "gpt-4o",
          "content": "LLM response",
          "cost": 0.02,
          "tokens": {
            "prompt": 150,
            "completion": 200,
            "total": 350
          }
        }
      ]
    }
  ]
}
```

### Cost Tracking
- **Token Usage**: Prompt and completion tokens per model
- **Cost Calculation**: Estimated costs based on current pricing
- **Total Tracking**: Aggregated costs across all steps and models

### Streaming Output
Real-time display during execution:
```
[Executor] Starting sequence: 0001
[Step a] Template: Instruction Converter
[Model: gpt-4o] Processing...
[Model: gpt-4o] Response: [Generated content]
[Cost] Step total: $0.02
```

## Advanced Usage Patterns

### Multi-Step Workflows
```bash
# Execute a complete analysis workflow
python lvl1_sequence_executor.py \
  --sequence "0010:a-d" \
  --models "gpt-4o-openai,claude-3-sonnet" \
  --chain-mode \
  "Analyze this business proposal"
```

### Comparative Analysis
```bash
# Compare multiple models on the same task
python lvl1_sequence_executor.py \
  --sequence 0001 \
  --models "gpt-4o-openai,claude-3-sonnet,gemini-pro" \
  --no-chain-mode \
  "Summarize this research paper"
```

### Keyword-Based Execution
```bash
# Execute all templates related to distillation
python lvl1_sequence_executor.py \
  --sequence "keyword:distill" \
  --models gpt-4o-openai \
  "Extract the essence of this complex document"
```

### Aggregation Workflows
```bash
# Run multiple approaches and aggregate results
python lvl1_sequence_executor.py \
  --sequence "0010:a-c" \
  --aggregator 0100 \
  --aggregator-inputs "a,b,c" \
  --models gpt-4o-openai \
  "Complex analysis task"
```

## Error Handling

### Template Validation
- **Format Checking**: Validates three-part template structure
- **Fallback Processing**: Graceful handling of non-compliant templates
- **Warning System**: Alerts for validation issues without stopping execution

### Execution Resilience
- **Retry Logic**: Automatic retries for failed API calls
- **Timeout Handling**: Configurable request timeouts
- **Error Recovery**: Continues execution despite individual step failures

### Output Integrity
- **JSON Validation**: Ensures valid JSON output structure
- **Streaming Safety**: Handles interruptions gracefully
- **Cost Tracking**: Maintains accurate cost calculations even with failures

## Performance Optimization

### Asynchronous Execution
- **Concurrent Processing**: Multiple models executed simultaneously
- **Streaming Output**: Real-time results without waiting for completion
- **Resource Management**: Efficient handling of API rate limits

### Caching Strategy
- **Catalog Caching**: Avoids regeneration when templates unchanged
- **Template Validation**: Cached validation results
- **Model Response**: Optional response caching for development

### Memory Management
- **Streaming JSON**: Incremental output writing for large results
- **Template Loading**: On-demand template loading
- **Garbage Collection**: Proper cleanup of large response objects

## Integration Examples

### Batch Processing
```python
# Process multiple inputs programmatically
import asyncio
from lvl1_sequence_executor import execute_sequence, ExecutorConfig

async def batch_process(inputs, sequence_id):
    results = []
    for input_text in inputs:
        config = ExecutorConfig(
            sequence_steps=get_sequence_steps(sequence_id),
            user_prompt=input_text,
            models=["gpt-4o-openai"]
        )
        result = await execute_sequence(config=config)
        results.append(result)
    return results
```

### Custom Workflows
```python
# Create custom execution workflows
config = ExecutorConfig(
    sequence_steps=custom_sequence,
    user_prompt="Custom input",
    models=["gpt-4o-openai", "claude-3-sonnet"],
    chain_mode=True,
    show_responses=False,
    output_file="custom_output.json"
)

results = await execute_sequence(config=config)
```

## Troubleshooting

### Common Issues

#### **Template Not Found**
- Verify sequence ID exists in catalog
- Check template file naming convention
- Regenerate catalog with `--force-regenerate`

#### **Model Access Errors**
- Verify API keys are set in environment
- Check model availability and permissions
- Validate model names against supported list

#### **Output File Issues**
- Ensure output directory exists and is writable
- Check file permissions
- Verify disk space availability

#### **Performance Issues**
- Reduce number of concurrent models
- Increase timeout values
- Check network connectivity and API rate limits

### Debug Mode
```bash
# Enable verbose logging
python lvl1_sequence_executor.py --sequence 0001 --debug "Test input"
```

### Validation Tools
```bash
# Test template catalog
python templates/lvl1_md_to_json.py --api-test

# Validate specific template
python lvl1_sequence_executor.py --sequence 0001 --validate-only
```
