"""
Stage 3 production management with manual precision control.

Provides full manual control for final architectural placement.
"""

import os
import glob
import shutil
import re
from typing import Dict, List, Any, Optional
from .catalog import LIFECYCLE_STAGES, TemplateConfigMD


class ProductionManager:
    """Manual control for production template placement."""
    
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.stage2_dir = os.path.join(self.script_dir, "md", "stage2")
        self.stage3_dir = os.path.join(self.script_dir, "md", "stage3")
    
    def promote_from_stage2(self, stage2_id: str, target_id: int = None) -> str:
        """Promote stage2 template with manual ID specification."""
        
        # Validate stage2 template exists
        stage2_file = self._find_template_file(stage2_id)
        if not stage2_file:
            raise FileNotFoundError(f"Stage2 template {stage2_id} not found")
        
        if target_id is None:
            target_id = self._interactive_id_selection(stage2_id, stage2_file)
        
        # Validate target ID
        self._validate_production_id(target_id)
        
        # Execute promotion
        new_id = self._execute_promotion(stage2_id, stage2_file, target_id)
        return new_id
    
    def _find_template_file(self, template_id: str) -> Optional[str]:
        """Find template file by ID in stage2 directory."""
        pattern = os.path.join(self.stage2_dir, f"{template_id}-*.md")
        matches = glob.glob(pattern)
        return matches[0] if matches else None
    
    def _interactive_id_selection(self, stage2_id: str, filepath: str) -> int:
        """Interactive ID selection with architecture guidance."""
        
        print(f"\nPromoting template {stage2_id} to Stage 3 (Production)")
        print("This requires manual ID specification for precise architecture placement.")
        
        # Display template analysis
        analysis = self._analyze_template(stage2_id, filepath)
        self._display_analysis(analysis)
        
        # Display current stage3 organization
        self._display_production_architecture()
        
        # Manual ID input with validation
        while True:
            try:
                target_id = int(input("\nEnter production ID (3000-3999): "))
                self._validate_production_id(target_id)
                
                # Confirm placement
                confirm = input(f"Confirm placement at ID {target_id}? (y/n): ")
                if confirm.lower() == 'y':
                    return target_id
                else:
                    print("Please choose a different ID.")
                    
            except ValueError:
                print("Please enter a valid number.")
            except Exception as e:
                print(f"Invalid ID: {e}")
    
    def _analyze_template(self, template_id: str, filepath: str) -> Dict[str, Any]:
        """Analyze template for display."""
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract template components
        config = TemplateConfigMD()
        stage = config.get_template_stage(template_id)
        category = config.get_template_category(template_id)
        
        # Basic content analysis
        title_match = re.search(r'\[(.*?)\]', content)
        title = title_match.group(1) if title_match else "Unknown"
        
        # Extract interpretation and transformation
        interpretation_match = re.search(r'\](.*?)Execute as:', content)
        interpretation = interpretation_match.group(1).strip() if interpretation_match else ""
        
        transformation_match = re.search(r'Execute as:\s*(`\{.*?\}`)', content)
        transformation = transformation_match.group(1) if transformation_match else ""
        
        return {
            "template_id": template_id,
            "title": title,
            "stage": stage,
            "category": category,
            "interpretation": interpretation,
            "transformation": transformation,
            "content_length": len(content)
        }
    
    def _display_analysis(self, analysis: Dict[str, Any]):
        """Display template analysis for user review."""
        print(f"\n=== Template Analysis ===")
        print(f"ID: {analysis['template_id']}")
        print(f"Title: {analysis['title']}")
        print(f"Current Stage: {analysis['stage']}")
        print(f"Category: {analysis['category']}")
        print(f"Content Length: {analysis['content_length']} characters")
        
        if analysis['interpretation']:
            print(f"\nInterpretation: {analysis['interpretation'][:100]}...")
        
        if analysis['transformation']:
            print(f"Transformation: {analysis['transformation'][:100]}...")
    
    def _display_production_architecture(self):
        """Display current stage3 organization for reference."""
        print(f"\n=== Current Production Architecture (Stage 3) ===")
        
        existing_templates = self._get_stage3_templates()
        if not existing_templates:
            print("No production templates yet. You're creating the first one!")
            print("Suggested ranges:")
            print("  3000-3099: Core foundational templates")
            print("  3100-3199: Primary operational templates")
            print("  3200-3299: Specialized domain templates")
            print("  3300-3399: Advanced transformation templates")
            print("  3400-3499: Integration templates")
            print("  3500-3999: Reserved for future architecture")
        else:
            # Group by hundreds for display
            grouped = {}
            for template_id in existing_templates:
                try:
                    numeric_id = int(template_id)
                    group = (numeric_id // 100) * 100
                    if group not in grouped:
                        grouped[group] = []
                    grouped[group].append(template_id)
                except ValueError:
                    continue
            
            for group in sorted(grouped.keys()):
                print(f"  {group}-{group+99}: {len(grouped[group])} templates")
                for template_id in sorted(grouped[group])[:3]:  # Show first 3
                    template_file = self._find_template_file(template_id)
                    if template_file:
                        title = self._extract_title_from_file(template_file)
                        print(f"    {template_id}: {title}")
                if len(grouped[group]) > 3:
                    print(f"    ... and {len(grouped[group]) - 3} more")
    
    def _get_stage3_templates(self) -> List[str]:
        """Get all stage3 template IDs."""
        stage3_templates = []
        
        pattern = os.path.join(self.full_templates_dir, "3*.md")
        for filepath in glob.glob(pattern):
            filename = os.path.basename(filepath)
            try:
                template_id = filename.split('-')[0]
                if 3000 <= int(template_id) <= 3999:
                    stage3_templates.append(template_id)
            except (ValueError, IndexError):
                continue
        
        return stage3_templates
    
    def _extract_title_from_file(self, filepath: str) -> str:
        """Extract title from template file."""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            title_match = re.search(r'\[(.*?)\]', content)
            return title_match.group(1) if title_match else "Unknown"
        except Exception:
            return "Unknown"
    
    def _validate_production_id(self, target_id: int):
        """Validate production ID availability and range."""
        if not (3000 <= target_id <= 3999):
            raise ValueError("Production ID must be in range 3000-3999")
        
        existing_ids = self._get_existing_ids_in_range(3000, 3999)
        if target_id in existing_ids:
            raise ValueError(f"ID {target_id} already exists in production")
    
    def _get_existing_ids_in_range(self, start: int, end: int) -> set:
        """Get all existing template IDs in specified range."""
        existing_ids = set()
        
        pattern = os.path.join(self.full_templates_dir, "*.md")
        for filepath in glob.glob(pattern):
            filename = os.path.basename(filepath)
            try:
                numeric_id = int(filename.split('-')[0])
                if start <= numeric_id <= end:
                    existing_ids.add(numeric_id)
            except (ValueError, IndexError):
                continue
        
        return existing_ids
    
    def _execute_promotion(self, old_id: str, old_filepath: str, new_id: int) -> str:
        """Execute promotion by moving file from stage2 to stage3 with new ID."""

        # Ensure stage3 directory exists
        os.makedirs(self.stage3_dir, exist_ok=True)

        # Read original content
        with open(old_filepath, 'r', encoding='utf-8') as f:
            content = f.read()

        # Generate new filename
        old_filename = os.path.basename(old_filepath)
        filename_parts = old_filename.split('-', 1)
        if len(filename_parts) > 1:
            new_filename = f"{new_id}-{filename_parts[1]}"
        else:
            new_filename = f"{new_id}-production.md"

        # Write to stage3 directory
        new_filepath = os.path.join(self.stage3_dir, new_filename)
        with open(new_filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        # Handle test prompt file if it exists
        testprompt_old = old_filepath.replace('.md', '.testprompt')
        if os.path.exists(testprompt_old):
            testprompt_new = new_filepath.replace('.md', '.testprompt')
            shutil.copy2(testprompt_old, testprompt_new)
            os.remove(testprompt_old)

        # Remove original from stage2
        os.remove(old_filepath)

        print(f"Promoted to production: stage2/{old_filename} -> stage3/{new_filename}")
        return str(new_id)


def promote_to_stage3(template_id: str, target_id: int = None) -> str:
    """Simplified production promotion interface."""
    manager = ProductionManager()
    return manager.promote_from_stage2(template_id, target_id)

def manual_placement(template_id: str, target_id: int) -> str:
    """Direct manual placement with ID specification."""
    manager = ProductionManager()
    return manager.promote_from_stage2(template_id, target_id)

def list_stage2_templates() -> List[str]:
    """List all stage2 templates available for promotion."""
    manager = ProductionManager()
    stage2_templates = []
    
    pattern = os.path.join(manager.full_templates_dir, "2*.md")
    for filepath in glob.glob(pattern):
        filename = os.path.basename(filepath)
        try:
            template_id = filename.split('-')[0]
            if 2000 <= int(template_id) <= 2999:
                stage2_templates.append(template_id)
        except (ValueError, IndexError):
            continue
    
    return sorted(stage2_templates)

def list_stage3_templates() -> List[str]:
    """List all stage3 production templates."""
    manager = ProductionManager()
    return manager._get_stage3_templates()

def show_production_architecture():
    """Display current production architecture."""
    manager = ProductionManager()
    manager._display_production_architecture()

def suggest_production_ranges():
    """Display suggested production ID ranges."""
    print("\n=== Suggested Production ID Ranges ===")
    print("3000-3099: Core foundational templates")
    print("3100-3199: Primary operational templates") 
    print("3200-3299: Specialized domain templates")
    print("3300-3399: Advanced transformation templates")
    print("3400-3499: Integration and workflow templates")
    print("3500-3599: Meta and recursive templates")
    print("3600-3699: Optimization and performance templates")
    print("3700-3799: Quality assurance and validation templates")
    print("3800-3899: Documentation and reporting templates")
    print("3900-3999: Experimental production features")
